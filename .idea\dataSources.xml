<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="DataSourceManagerImpl" format="xml" multifile-model="true">
    <data-source source="LOCAL" name="category@localhost" uuid="47974e95-970b-4a02-a40e-a928cb0e93f8">
      <driver-ref>mysql_aurora_aws</driver-ref>
      <synchronize>true</synchronize>
      <jdbc-driver>software.aws.rds.jdbc.mysql.Driver</jdbc-driver>
      <jdbc-url>****************************************</jdbc-url>
      <jdbc-additional-properties>
        <property name="com.intellij.clouds.kubernetes.db.host.port" />
        <property name="com.intellij.clouds.kubernetes.db.enabled" value="false" />
        <property name="com.intellij.clouds.kubernetes.db.container.port" />
      </jdbc-additional-properties>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
  </component>
</project>