# Net教育项目运行指南

## 项目简介
这是一个基于Spring Boot的教育管理系统，集成了AI功能、文件存储、用户管理等功能。

## 环境要求
- JDK 21
- Maven 3.6+
- MySQL 8.0+
- MinIO (可选，用于文件存储)
- KKFileView (可选，用于文件预览)

## 快速启动

### 1. 数据库准备
```sql
-- 创建数据库
CREATE DATABASE net CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 导入数据表结构
mysql -u root -p net < sql/net.sql
```

### 2. 配置修改
编辑 `src/main/resources/application-dev.yml` 文件，修改以下配置：

```yaml
# 数据库配置
spring:
  datasource:
    url: *********************************************************************************************************************************************
    username: root  # 修改为您的MySQL用户名
    password: 123456  # 修改为您的MySQL密码

# MinIO配置 (可选，如果不需要文件存储功能可以留空)
minio:
  endpoint: http://localhost:9000
  accessKey: minioadmin
  secretKey: minioadmin
  bucket: net-edu

# KKFileView配置 (可选，用于文件预览)
kkfileview:
  url: http://localhost:8012

# Coze AI配置 (可选，如果不需要AI功能可以留空)
coze:
  streamUrl: http://localhost:8080/api/stream
  url: http://localhost:8080/api
  token: your-coze-token-here
  assistantWorkflowId: your-assistant-workflow-id
  markPaperWorkflowId: your-mark-paper-workflow-id
  brainWorkflowId: your-brain-workflow-id
  makePracticeWorkflowId: your-make-practice-workflow-id
```

### 3. 启动项目

#### 方式一：使用批处理文件（推荐）
双击运行 `start.bat` 文件

#### 方式二：使用命令行
```bash
# 清理并编译
mvn clean compile

# 启动应用
mvn spring-boot:run
```

#### 方式三：使用IDE
在IDE中直接运行 `NetApplication.java` 的main方法

### 4. 验证启动
应用启动成功后，访问以下地址：
- 应用主页: http://localhost:8099
- Swagger API文档: http://localhost:8099/swagger-ui.html
- CORS测试接口: http://localhost:8099/test/cors

## 错误诊断

### 如果启动失败，请运行诊断工具：
```bash
# 双击运行
diagnose.bat
```

诊断工具会检查：
- Java环境是否正确安装
- Maven环境是否正确配置
- MySQL服务是否启动
- 端口是否被占用
- 项目文件是否完整
- 项目是否能正常编译

### 查看详细日志
```bash
# 查看实时日志
tail -f logs/net.log

# 或查看控制台输出
mvn spring-boot:run
```

## 可选服务配置

### MinIO文件存储服务
如果需要文件上传功能，可以安装MinIO：

1. **下载MinIO**
   ```bash
   # Windows
   wget https://dl.min.io/server/minio/release/windows-amd64/minio.exe
   
   # 或访问官网下载: https://min.io/download
   ```

2. **启动MinIO**
   ```bash
   minio server C:\minio --console-address :9001
   ```

3. **访问MinIO控制台**
   - 地址: http://localhost:9001
   - 用户名: minioadmin
   - 密码: minioadmin

### KKFileView文件预览服务
如果需要文件预览功能，可以安装KKFileView：

1. **下载KKFileView**
   ```bash
   # 访问: https://kkfileview.keking.cn/
   ```

2. **启动KKFileView**
   ```bash
   java -jar kkFileView-4.1.0.jar
   ```

3. **访问KKFileView**
   - 地址: http://localhost:8012

## 常见问题

### 1. 端口被占用
如果8099端口被占用，修改 `application-dev.yml` 中的端口配置：
```yaml
server:
  port: 8080  # 修改为其他可用端口
```

### 2. 数据库连接失败
- 检查MySQL服务是否启动
- 检查数据库用户名密码是否正确
- 检查数据库名称是否为 `net`

### 3. Nacos配置问题
项目默认使用Nacos作为配置中心，如果Nacos不可用：
- 已通过 `application-dev.yml` 提供本地配置
- 已注释掉 `bootstrap.yml` 中的Nacos配置导入

### 4. MinIO配置问题
如果不需要文件存储功能：
- 可以将MinIO配置留空，应用仍可正常启动
- 文件上传功能将不可用

### 5. Coze AI配置
如果不需要AI功能，可以将 `coze` 配置留空，应用仍可正常启动。

### 6. CORS跨域问题
如果前端访问后端API时遇到CORS错误：

#### 问题分析
错误信息显示前端访问的是远程服务器 `http://************:8099`，而不是本地服务器。

#### 解决方案1：修改前端配置
将前端请求的API地址改为本地服务器：
```javascript
// 将
const API_BASE_URL = 'http://************:8099'
// 改为
const API_BASE_URL = 'http://localhost:8099'
```

#### 解决方案2：重启后端服务
项目已配置完整的CORS支持，包括：
- 全局CORS过滤器 (`SimpleCorsFilter`)
- 控制器级别的CORS注解
- WebMvcConfig中的CORS配置

重启后端服务后，CORS问题应该解决。

#### 解决方案3：测试CORS配置
运行测试脚本验证CORS是否正常：
```bash
# 双击运行
test-cors.bat

# 或手动测试
curl -X GET http://localhost:8099/test/cors -v
curl -X POST http://localhost:8099/test/cors -H "Content-Type: application/json" -d "test" -v
curl -X OPTIONS http://localhost:8099/test/cors -v
```

#### 解决方案4：检查网络连接
如果必须使用远程服务器：
1. 确保远程服务器已启动
2. 确保远程服务器有正确的CORS配置
3. 检查防火墙设置
4. 检查网络连接

#### 解决方案5：使用代理
如果问题持续存在，可以在前端开发服务器中配置代理：
```javascript
// vite.config.js
export default {
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:8099',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  }
}
```

### 7. Bean定义冲突
如果遇到Bean定义冲突错误：
- 项目已配置 `spring.main.allow-bean-definition-overriding: true`
- 如果仍有问题，请检查是否有重复的配置类

## 项目结构
```
src/main/java/org/zc/net/
├── config/          # 配置类
├── controller/      # 控制器
├── entity/         # 实体类
├── mapper/         # MyBatis映射器
├── service/        # 服务层
├── util/           # 工具类
└── NetApplication.java  # 主启动类
```

## 技术栈
- Spring Boot 3.2.2
- MyBatis Plus 3.5.5
- MySQL 8.0
- MinIO (文件存储)
- KKFileView (文件预览)
- Nacos (配置中心)
- JWT (身份认证)
- Swagger (API文档)

## 开发建议
1. 使用 `dev` 环境进行本地开发
2. 修改配置后重启应用
3. 查看控制台日志进行问题排查
4. 使用Swagger UI测试API接口
5. 如果遇到CORS问题，先测试 `/test/cors` 接口
6. 确保前端和后端使用相同的服务器地址
7. 可选服务（MinIO、KKFileView）不影响核心功能
8. 遇到问题时先运行 `diagnose.bat` 进行诊断 