# 项目概述
将新一代AI大模型技术与计算机网络课程教学平台结合起来，有助于提高教学效率及教学质量。本系统尝试将AI应用融入计算机网络课程学习平台。

# 系统功能模块
在本项目中，核心的功能模块可以进一步划分为学生模块和教师模块。
## 学生端
- 个人信息模块
- 资源模块
- 测验和作业模块
- AI交互模块
## 教师端
- 班级管理模块
- 资源管理模块
- 测验和作业管理模块
- 题库管理模块
- 知识点管理模块

# 系统架构
- 表示层：与用户直接交互，获取用户输入并展示数据，这一层采用Vue框架进行编写。
- 业务逻辑层：负责系统核心的功能逻辑，这一层采用Spring Boot框架进行编写。
- 数据持久层：负责数据的存储和检索，包括MySQL关系数据库和MinIO对象数据库。
- 代理层：处理直接与大模型交互的业务，这一层在Coze平台上进行开发。
- 模型层：提供核心模型支持，提供自然语言处理、图像理解等能力。

# 数据库概述
MySQL数据库包括9张实体表和7张联系表。
实体表包括student表、teacher表、course表、resource表、category表、exam表、question表、tag_node表、chat表。
联系表包括course_student表、category_resource表、exam_question表、student_exam表、student_exam_question表、tag_relationship表、student_tag表。