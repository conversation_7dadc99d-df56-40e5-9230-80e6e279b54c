@echo off
echo ========================================
echo Net教育项目错误诊断工具
echo ========================================
echo.

echo 1. 检查Java环境...
java -version
if %errorlevel% neq 0 (
    echo [错误] Java环境检查失败
    echo 请确保已安装JDK 21并配置环境变量
    goto :error
)
echo [成功] Java环境正常
echo.

echo 2. 检查Maven环境...
mvn -version
if %errorlevel% neq 0 (
    echo [错误] Maven环境检查失败
    echo 请确保已安装Maven并配置环境变量
    goto :error
)
echo [成功] Maven环境正常
echo.

echo 3. 检查MySQL服务...
net start | findstr "MySQL" >nul
if %errorlevel% neq 0 (
    echo [警告] MySQL服务可能未启动
    echo 建议启动MySQL服务: net start MySQL80
) else (
    echo [成功] MySQL服务正在运行
)
echo.

echo 4. 检查端口占用...
netstat -ano | findstr :8099
if %errorlevel% equ 0 (
    echo [警告] 端口8099已被占用
    echo 请停止占用端口的进程或修改配置文件中的端口
) else (
    echo [成功] 端口8099可用
)
echo.

echo 5. 检查项目文件...
if not exist "src\main\java\org\zc\net\NetApplication.java" (
    echo [错误] 主启动类文件不存在
    goto :error
)
if not exist "src\main\resources\application-dev.yml" (
    echo [错误] 开发环境配置文件不存在
    goto :error
)
echo [成功] 项目文件结构正常
echo.

echo 6. 尝试编译项目...
mvn clean compile -q
if %errorlevel% neq 0 (
    echo [错误] 项目编译失败
    echo 请检查代码语法或依赖配置
    goto :error
)
echo [成功] 项目编译通过
echo.

echo ========================================
echo [成功] 所有检查通过，项目应该可以正常启动
echo ========================================
echo.
echo 启动命令: mvn spring-boot:run
echo 或双击运行: start.bat
echo.
echo 如果仍有问题，请查看详细错误日志
echo 日志文件: logs/net.log
echo ========================================
goto :end

:error
echo.
echo ========================================
echo [错误] 诊断发现问题，请根据上述提示进行修复
echo ========================================
echo.
echo 常见解决方案:
echo 1. 安装JDK 21: https://adoptium.net/
echo 2. 安装Maven: https://maven.apache.org/download.cgi
echo 3. 启动MySQL: net start MySQL80
echo 4. 修改端口: 编辑 application-dev.yml 中的 server.port
echo 5. 查看日志: tail -f logs/net.log
echo ========================================

:end
pause 