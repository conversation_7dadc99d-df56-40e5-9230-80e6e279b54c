@echo off
echo ========================================
echo Net教育项目 - Java直接启动
echo ========================================
echo.

echo 1. 检查Java环境...
java -version
if %errorlevel% neq 0 (
    echo [错误] Java环境检查失败
    pause
    exit /b 1
)
echo [成功] Java环境正常
echo.

echo 2. 检查编译后的类文件...
if not exist "target\classes\org\zc\net\NetApplication.class" (
    echo [错误] 项目未编译，请先编译项目
    echo 建议使用IDE编译项目，或安装Maven
    pause
    exit /b 1
)
echo [成功] 找到编译后的类文件
echo.

echo 3. 检查依赖库...
if not exist "target\lib" (
    echo [警告] 依赖库目录不存在，可能需要手动下载依赖
    echo 建议使用IDE或Maven管理依赖
)
echo.

echo 4. 启动应用...
echo [信息] 应用将在 http://localhost:8099 启动
echo [信息] 按 Ctrl+C 停止应用
echo ========================================

cd target\classes
java -cp ".;..\lib\*" org.zc.net.NetApplication

pause 