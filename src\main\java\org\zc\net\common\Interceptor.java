package org.zc.net.common;

import com.fasterxml.jackson.databind.ObjectMapper;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.servlet.HandlerInterceptor;
import org.zc.net.util.JwtUtil;

import java.io.PrintWriter;

/**
 * HTTP拦截器
 */
public class Interceptor implements HandlerInterceptor {

    /**
     * 在Controller方法处理请求之前执行，检查token的有效性
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String token = request.getHeader("Authorization");

        if(token == null || JwtUtil.parseToken(token) == null) { // token不合法
            response.setContentType("application/json;charset=utf-8");
            Result<Object> result = Result.error(Code.UNAUTHORIZED, "请重新登录");

            // 获取响应的输出流，准备写入JSON响应数据
            try (PrintWriter out = response.getWriter()) {
                // 使用ObjectMapper将Result对象序列化为JSON字符串
                ObjectMapper objectMapper = new ObjectMapper();
                // 将JSON字符串写入响应输出流，发送给客户端
                out.write(objectMapper.writeValueAsString(result));
            }

            return false;
        }

        return true;
    }

}
