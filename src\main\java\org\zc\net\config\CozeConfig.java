package org.zc.net.config;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;

@Configuration
public class CozeConfig {

    @Value("${coze.streamUrl:http://localhost:8080/api/stream}")
    private String streamUrl;

    @Value("${coze.url:http://localhost:8080/api}")
    private String url;

    @Value("${coze.token:}")
    private String token;

    @Value("${coze.assistantWorkflowId:}")
    private String assistantWorkflowId;

    @Value("${coze.markPaperWorkflowId:}")
    private String markPaperWorkflowId;

    @Value("${coze.brainWorkflowId:}")
    private String brainWorkflowId;

    @Value("${coze.makePracticeWorkflowId:}")
    private String makePracticeWorkflowId;

    /*
    public Flux<String> getAIAnswer(JSONArray array, String msg) {
        JSONObject parameters = JSONUtil.createObj()
                .putOnce("memory", array)
                .putOnce("msg", msg);

        JSONObject requestBody = JSONUtil.createObj()
                .putOnce("workflow_id", assistantWorkflowId)
                .putOnce("parameters", parameters);

        return WebClient.create()
                .method(HttpMethod.POST)
                .uri(streamUrl)
                .header("Authorization", "Bearer " + token)
                .header("Content-Type", "application/json")
                .bodyValue(requestBody)
                .retrieve()
                .bodyToFlux(String.class);
    }

    public JSONArray markPaper(JSONArray array) {
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization","Bearer " + token);
        headers.set("Content-Type","application/json");

        JSONObject parameters = JSONUtil.createObj()
                .putOnce("input", array);

        JSONObject requestBody = JSONUtil.createObj()
                .putOnce("workflow_id", markPaperWorkflowId)
                .putOnce("parameters", parameters);

        HttpEntity<String> entity = new HttpEntity<>(requestBody.toString(), headers);
        ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);
        JSONObject a = JSONUtil.parseObj(response.getBody());
        JSONObject b = JSONUtil.parseObj(a.getStr("data"));
        JSONArray c = JSONUtil.parseArray(b.getStr("output"));
        return c;
    }

    public String analyzeTag(String msg) {
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization","Bearer " + token);
        headers.set("Content-Type","application/json");

        JSONObject parameters = JSONUtil.createObj()
                .putOnce("input", msg);

        JSONObject requestBody = JSONUtil.createObj()
                .putOnce("workflow_id", brainWorkflowId)
                .putOnce("parameters", parameters);

        HttpEntity<String> entity = new HttpEntity<>(requestBody.toString(), headers);
        ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);
        JSONObject a = JSONUtil.parseObj(response.getBody());
        JSONObject b = JSONUtil.parseObj(a.getStr("data"));
        String s = b.getStr("data");
        return s;
    }

    public JSONObject makePractice(String tag, Double mastery) {
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization","Bearer " + token);
        headers.set("Content-Type","application/json");

        JSONObject parameters = JSONUtil.createObj()
                .putOnce("tag", tag)
                .putOnce("mastery", mastery);

        JSONObject requestBody = JSONUtil.createObj()
                .putOnce("workflow_id", makePracticeWorkflowId)
                .putOnce("parameters", parameters);

        HttpEntity<String> entity = new HttpEntity<>(requestBody.toString(), headers);
        ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);
        JSONObject a = JSONUtil.parseObj(response.getBody());
        JSONObject b = JSONUtil.parseObj(a.getStr("data"));
        JSONObject c = JSONUtil.parseObj(b.getStr("output"));
        return c;
    }
    */

}
