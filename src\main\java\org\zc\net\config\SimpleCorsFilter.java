package org.zc.net.config;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * 简单CORS过滤器
 * 确保在所有响应中都添加CORS头
 * 
 * <AUTHOR>
 * @date 2023-05-15
 */
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public class SimpleCorsFilter implements Filter {
    
    private static final Logger logger = LoggerFactory.getLogger(SimpleCorsFilter.class);

    @Override
    public void doFilter(ServletRequest req, ServletResponse res, FilterChain chain) throws IOException, ServletException {
        HttpServletResponse response = (HttpServletResponse) res;
        HttpServletRequest request = (HttpServletRequest) req;

        try {
            // 设置CORS头
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS, PATCH");
            response.setHeader("Access-Control-Allow-Headers", "*");
            response.setHeader("Access-Control-Expose-Headers", "*");
            response.setHeader("Access-Control-Allow-Credentials", "true");
            response.setHeader("Access-Control-Max-Age", "3600");

            // 处理OPTIONS预检请求
            if ("OPTIONS".equalsIgnoreCase(request.getMethod())) {
                logger.debug("处理OPTIONS预检请求: {}", request.getRequestURI());
                response.setStatus(HttpServletResponse.SC_OK);
                return;
            }

            chain.doFilter(req, res);
            
        } catch (Exception e) {
            logger.error("CORS过滤器处理请求时发生错误: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public void init(FilterConfig filterConfig) {
        logger.info("SimpleCorsFilter初始化完成");
    }

    @Override
    public void destroy() {
        logger.info("SimpleCorsFilter销毁完成");
    }
} 