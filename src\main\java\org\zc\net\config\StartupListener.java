package org.zc.net.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

/**
 * 启动监听器
 * 提供详细的启动信息和错误提示
 * 
 * <AUTHOR>
 * @date 2023-05-15
 */
@Configuration
public class StartupListener implements ApplicationListener<ApplicationReadyEvent> {
    
    private static final Logger logger = LoggerFactory.getLogger(StartupListener.class);

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        Environment env = event.getApplicationContext().getEnvironment();
        String port = env.getProperty("server.port", "8099");
        String contextPath = env.getProperty("server.servlet.context-path", "");
        
        logger.info("==========================================");
        logger.info("🎉 Net教育项目启动成功！");
        logger.info("==========================================");
        logger.info("📱 应用地址: http://localhost:{}{}", port, contextPath);
        logger.info("📚 Swagger文档: http://localhost:{}{}/swagger-ui.html", port, contextPath);
        logger.info("🧪 CORS测试: http://localhost:{}{}/test/cors", port, contextPath);
        logger.info("==========================================");
        logger.info("💡 开发提示:");
        logger.info("   - 如果遇到CORS问题，请检查前端配置");
        logger.info("   - 如果数据库连接失败，请检查MySQL服务");
        logger.info("   - 如果文件上传失败，请检查MinIO配置");
        logger.info("   - 查看详细日志: tail -f logs/net.log");
        logger.info("==========================================");
    }
} 