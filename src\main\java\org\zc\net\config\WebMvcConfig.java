package org.zc.net.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.zc.net.common.Interceptor;

/**
 * Web MVC配置
 * 
 * <AUTHOR>
 * @date 2023-05-15
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    /**
     * 跨域配置
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOriginPatterns("*")  // 允许所有来源
                .allowedMethods("GET", "HEAD", "POST", "PUT", "DELETE", "OPTIONS", "PATCH")  // 允许的HTTP方法
                .allowedHeaders("*")  // 允许所有请求头
                .exposedHeaders("*")  // 暴露的响应头
                .allowCredentials(true)  // 允许携带认证信息
                .maxAge(3600);  // 预检请求的有效期
    }

    /**
     * 拦截器配置
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new Interceptor())
                .addPathPatterns("/**") // 拦截所有路径
                .excludePathPatterns(
                        "/student/login",
                        "/student/register",
                        "/teacher/login",
                        "/teacher/register",
                        "/swagger-ui/**",
                        "/v3/**",
                        "/swagger-resources/**",
                        "/webjars/**"
                ); // 排除路径
    }

}
