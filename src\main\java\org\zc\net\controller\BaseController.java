package org.zc.net.controller;

import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * 基础控制器
 * 包含通用的CORS配置
 * 
 * <AUTHOR>
 * @date 2023-05-15
 */
@CrossOrigin(
    origins = "*",
    allowedHeaders = "*",
    methods = {
        RequestMethod.GET,
        RequestMethod.POST,
        RequestMethod.PUT,
        RequestMethod.DELETE,
        RequestMethod.OPTIONS,
        RequestMethod.PATCH
    },
    allowCredentials = "true",
    maxAge = 3600
)
public abstract class BaseController {
    // 基础控制器，所有控制器可以继承此类来获得CORS支持
} 