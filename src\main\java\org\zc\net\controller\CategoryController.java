package org.zc.net.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.zc.net.common.Result;
import org.zc.net.entity.Category;
import org.zc.net.entity.vo.CategoryVo;
import org.zc.net.service.impl.CategoryService;

import java.util.List;

@RestController
@RequestMapping("/category")
@Tag(name = "CategoryController", description = "分组管理Controller")
public class CategoryController {

    @Autowired
    CategoryService categoryService;

    @GetMapping("/{id}")
    @Operation(summary = "获取指定ID的分组信息")
    public Result<Category> selectById(@PathVariable("id") Long id) {
        return Result.success(categoryService.selectById(id));
    }

    @GetMapping
    @Operation(summary = "获取分组树")
    public Result<List<CategoryVo>> selectTree(@RequestParam("type") String type) {
        return Result.success(categoryService.selectTree(type));
    }

    @PostMapping
    @Operation(summary = "添加分组")
    public Result<Object> insert(@RequestBody Category category) {
        return Result.success("添加成功", categoryService.insert(category));
    }

    @PutMapping
    @Operation(summary = "修改分组")
    public Result<Object> updateById(@RequestBody Category category) {
        return Result.success("修改成功", categoryService.updateById(category));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除分组")
    public Result<Object> deleteById(@PathVariable("id") Long id) {
        return Result.success("删除成功", categoryService.deleteById(id));
    }

    @PostMapping("/{id}/resource")
    @Operation(summary = "在分组下添加资源")
    public Result<Object> insertResource(@PathVariable("id") Long id,
                                         @RequestBody Long[] resourceIds) {
        return Result.success("添加成功", categoryService.insertResource(id, resourceIds));
    }

    @DeleteMapping("/{id}/resource/{resourceId}")
    @Operation(summary = "删除分组下的资源")
    public Result<Object> deleteResource(@PathVariable("id") Long id,
                                         @PathVariable("resourceId") Long resourceId) {
        return Result.success("删除成功", categoryService.deleteResource(id, resourceId));
    }

}
