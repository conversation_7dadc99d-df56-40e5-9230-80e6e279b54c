package org.zc.net.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import org.zc.net.common.Result;
import org.zc.net.entity.Chat;
import org.zc.net.entity.dto.ChatDto;
import org.zc.net.service.impl.ChatService;

import java.util.List;

@RestController
@RequestMapping("/chat")
@Tag(name = "ChatController", description = "AI管理Controller")
public class ChatController {

    @Autowired
    ChatService chatService;

    @PostMapping("/brain")
    public Result<String> getCoze(@RequestBody ChatDto dto) {
        return Result.success(chatService.getCoze(dto));
    }

    @PostMapping("/exam/{examId}/student/{studentId}")
    public Result<String> postCoze(@PathVariable("examId") Long examId,
                                   @PathVariable("studentId") Long studentId) {
        return Result.success(chatService.postCoze(examId, studentId));
    }

    @PostMapping(value = "/coze/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Operation(summary = "流式获取AI回答")
    public SseEmitter streamCoze(@RequestBody ChatDto dto) {
        return chatService.streamCoze(dto);
    }

    @GetMapping("/history/{id}")
    @Operation(summary = "获取对话历史")
    public Result<List<Chat>> selectHistory(@PathVariable("id") Long id) {
        return Result.success(chatService.selectHistory(id));
    }

    @DeleteMapping("/history/{id}")
    @Operation(summary = "删除对话历史")
    public Result<Object> deleteHistory(@PathVariable("id") Long id) {
        return Result.success("删除成功", chatService.deleteHistory(id));
    }

}
