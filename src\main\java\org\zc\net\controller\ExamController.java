package org.zc.net.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.zc.net.common.Result;
import org.zc.net.entity.Exam;
import org.zc.net.entity.dto.*;
import org.zc.net.entity.vo.*;
import org.zc.net.service.impl.ExamService;
import org.zc.net.util.JwtUtil;

import java.util.List;

@RestController
@RequestMapping("/exam")
@Tag(name = "ExamController", description = "测验管理Controller")
public class ExamController {

    @Autowired
    ExamService examService;

    @GetMapping("/{id}")
    @Operation(summary = "获取指定ID的测验信息")
    public Result<ExamVo> selectById(@PathVariable("id") Long id){
        return Result.success(examService.selectById(id));
    }

    @GetMapping
    @Operation(summary = "获取条件分页测验信息")
    public Result<PageVo<Exam>> selectPage(@RequestParam(name = "pageNum", defaultValue = "1") Long pageNum,
                                           @RequestParam(name = "pageSize", defaultValue = "10") Long pageSize,
                                           Exam exam){
        return Result.success(examService.selectPage(pageNum, pageSize, exam));
    }

    @PostMapping
    @Operation(summary = "添加测验")
    public Result<Object> insert(@RequestBody Exam exam){
        return Result.success("添加成功", examService.insert(exam));
    }

    @PutMapping
    @Operation(summary = "修改测验")
    public Result<Object> updateById(@RequestBody Exam exam){
        return Result.success("修改成功", examService.updateById(exam));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除测验")
    public Result<Object> deleteById(@PathVariable("id") Long id){
        return Result.success("删除成功", examService.deleteById(id));
    }

    @GetMapping("/{examId}/start")
    @Operation(summary = "学生开始测验")
    public Result<StartExamVo> startExam(@PathVariable("examId") Long examId){
        return Result.success(examService.startExam(examId));
    }

    @PostMapping("/{examId}/end")
    @Operation(summary = "学生结束测验")
    public Result<Object> endExam(@PathVariable("examId") Long examId,
                                  @RequestBody List<QuestionDto> studentQuestions){
        return Result.success("交卷成功", examService.endExam(examId, studentQuestions));
    }

    @GetMapping("/{id}/result")
    @Operation(summary = "获取学生测验结果列表")
    public Result<PageVo<StudentExamVo>> selectStudentResultList(@PathVariable("id") Long id,
                                                                 @RequestParam(name = "pageNum", defaultValue = "1") Long pageNum,
                                                                 @RequestParam(name = "pageSize", defaultValue = "10") Long pageSize,
                                                                 @RequestParam(name = "status", defaultValue = "") String status){
        return Result.success(examService.selectStudentResultList(id, pageNum, pageSize, status));
    }

    @GetMapping("/{examId}/student/result")
    @Operation(summary = "学生获取测验结果")
    public Result<StudentExamResultVo> selectStudentResult(@PathVariable("examId") Long examId){
        return selectStudentResult(JwtUtil.getUserId(), examId);
    }

    @GetMapping("/{examId}/student/{studentId}/result")
    @Operation(summary = "获取学生测验结果")
    public Result<StudentExamResultVo> selectStudentResult(@PathVariable("studentId") Long studentId,
                                                           @PathVariable("examId") Long examId){
        return Result.success(examService.selectStudentResult(studentId, examId));
    }

    @PutMapping("/{examId}/student/{studentId}/result")
    @Operation(summary = "修改学生测验结果")
    public Result<Object> editStudentResult(@PathVariable("examId") Long examId,
                                            @PathVariable("studentId") Long studentId,
                                            @RequestBody List<QuestionDto> questions){
        return Result.success("修改成功", examService.editStudentResult(studentId, examId, questions));
    }

    @GetMapping("/{examId}/student/{studentId}/download")
    @Operation(summary = "导出学生答卷")
    public void downloadStudentPaper(HttpServletResponse response,
                                 @PathVariable("examId") Long examId,
                                 @PathVariable("studentId") Long studentId) {
        examService.downloadStudentPaper(response, examId, studentId);
    }

    @GetMapping("/{id}/question")
    @Operation(summary = "获取测验的试卷")
    public Result<List<QuestionDto>> selectQuestion(@PathVariable("id") Long id){
        return Result.success(examService.selectQuestion(id));
    }

    @PostMapping("/{id}/question")
    @Operation(summary = "添加试卷题目")
    public Result<Object> insertQuestion(@PathVariable("id") Long id,
                                         @RequestBody Long[] questionIds){
        return Result.success("添加成功", examService.insertQuestion(id, questionIds));
    }

    @PutMapping("/{id}/question")
    @Operation(summary = "修改试卷题目")
    public Result<Object> updateQuestion(@PathVariable("id") Long id,
                                         @RequestBody List<QuestionDto> questions){
        return Result.success("修改成功", examService.updateQuestion(id, questions));
    }

    @DeleteMapping("/{id}/question/{questionId}")
    @Operation(summary = "删除试卷题目")
    public Result<Object> deleteQuestion(@PathVariable("id") Long id,
                                         @PathVariable("questionId") Long questionId){
        return Result.success("删除成功", examService.deleteQuestion(id, questionId));
    }

    @GetMapping("/{id}/question/download")
    @Operation(summary = "导出试卷")
    public void downloadQuestion(HttpServletResponse response,
                                @PathVariable("id") Long id) {
        examService.downloadQuestion(response, id);
    }

    @GetMapping("/{id}/ranking")
    @Operation(summary = "获取学生成绩排名")
    public Result<PageVo<StudentExamVo>> selectRanking(@PathVariable("id") Long id,
                                                       @RequestParam(name = "pageNum", defaultValue = "1") Long pageNum,
                                                       @RequestParam(name = "pageSize", defaultValue = "10") Long pageSize){
        return Result.success(examService.selectRanking(id, pageNum, pageSize));
    }

    @GetMapping("/{id}/ranking/download")
    @Operation(summary = "导出学生成绩排名")
    public void downloadRanking(HttpServletResponse response,
                                @PathVariable("id") Long id) {
        examService.downloadRanking(response, id);
    }

    @GetMapping("/{id}/statistics")
    @Operation(summary = "获取测验的统计数据")
    public Result<StatisticsDto> selectStatistics(@PathVariable("id") Long id){
        return Result.success(examService.selectStatistics(id));
    }

}
