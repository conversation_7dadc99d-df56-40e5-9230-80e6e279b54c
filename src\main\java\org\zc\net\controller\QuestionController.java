package org.zc.net.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.zc.net.common.Code;
import org.zc.net.common.Result;
import org.zc.net.entity.Question;
import org.zc.net.entity.vo.PageVo;
import org.zc.net.service.impl.QuestionService;

import java.util.List;

@RestController
@RequestMapping("/question")
@Tag(name = "QuestionController", description = "题目管理Controller")
public class QuestionController {

    @Autowired
    QuestionService questionService;

    @GetMapping("/{id}")
    @Operation(summary = "获取指定ID的题目信息")
    public Result<Question> selectById(@PathVariable("id") Long id){
        return Result.success(questionService.selectById(id));
    }

    @GetMapping
    @Operation(summary = "获取条件分页题目列表")
    public Result<PageVo<Question>> selectPage(@RequestParam(name = "pageNum", defaultValue = "1") Long pageNum,
                                               @RequestParam(name = "pageSize", defaultValue = "10") Long pageSize,
                                               Question question){
        return Result.success(questionService.selectPage(pageNum, pageSize, question));
    }

    @PostMapping
    @Operation(summary = "添加题目")
    public Result<Integer> insert(@RequestBody Question question){
        return Result.success("添加成功", questionService.insert(question));
    }

    @PutMapping
    @Operation(summary = "修改题目")
    public Result<Integer> updateById(@RequestBody Question question){
        return Result.success("修改成功", questionService.updateById(question));
    }

    @DeleteMapping("/{ids}")
    @Operation(summary = "删除题目")
    public Result<Integer> deleteById(@PathVariable("ids") List<Long> ids){
        return Result.success("删除成功", questionService.deleteById(ids));
    }

}
