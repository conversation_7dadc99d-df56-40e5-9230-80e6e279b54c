package org.zc.net.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.zc.net.common.Result;
import org.zc.net.entity.Resource;
import org.zc.net.entity.vo.PageVo;
import org.zc.net.service.impl.ResourceService;

@RestController
@RequestMapping("/resource")
@Tag(name = "ResourceController", description = "资源管理Controller")
public class ResourceController {

    @Autowired
    ResourceService resourceService;

    @GetMapping("/{id}")
    @Operation(summary = "获取指定ID的资源信息")
    public Result<Resource> selectById(@PathVariable("id") Long id){
        return Result.success(resourceService.selectById(id));
    }

    @GetMapping
    @Operation(summary = "获取条件分页资源信息")
    public Result<PageVo<Resource>> selectPage(@RequestParam(name = "pageNum", defaultValue = "1") Long pageNum,
                                               @RequestParam(name = "pageSize", defaultValue = "10") Long pageSize,
                                               Resource resource){
        return Result.success(resourceService.selectPage(pageNum, pageSize, resource));
    }

    @PostMapping("/upload")
    @Operation(summary = "上传文件资源")
    public Result<Resource> upload(@RequestParam(name = "file", required = false) MultipartFile file){
        return Result.success(resourceService.upload(file));
    }

    @PostMapping
    @Operation(summary = "添加资源信息")
    public Result<Integer> insert(@RequestBody Resource resource){
        return Result.success("添加成功", resourceService.insert(resource));
    }

    @PutMapping
    @Operation(summary = "修改资源信息")
    public Result<Integer> updateById(@RequestBody Resource resource){
        return Result.success("修改成功", resourceService.updateById(resource));
    }

    @GetMapping("/{id}/download")
    @Operation(summary = "下载文件资源")
    public void download(HttpServletResponse response, @PathVariable("id") Long id){
        resourceService.download(response, id);
    }

    @GetMapping("/{id}/preview")
    @Operation(summary = "获取预览路径")
    public Result<String> getPreviewUrl(@PathVariable("id") Long id){
        return Result.success(resourceService.getPreviewUrl(id));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除资源信息")
    public Result<Integer> deleteById(@PathVariable("id") Long id) {
        return Result.success("删除成功", resourceService.deleteById(id));
    }

}
