package org.zc.net.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.zc.net.common.Result;
import org.zc.net.entity.Student;
import org.zc.net.entity.dto.LoginDto;
import org.zc.net.entity.dto.PasswordDto;
import org.zc.net.service.impl.StudentService;

/**
 * 学生管理控制器
 * 
 * <AUTHOR>
 * @date 2023-05-15
 */
@RestController
@RequestMapping("/student")
@Tag(name = "StudentController", description = "学生管理Controller")
public class StudentController extends BaseController {

    @Autowired
    StudentService studentService;

    @PostMapping("/register")
    @Operation(summary = "注册")
    public Result<Object> register(@RequestBody LoginDto loginDto){
        return Result.success("注册成功", studentService.register(loginDto));
    }

    @PostMapping("/login")
    @Operation(summary = "登录")
    public Result<String> login(@RequestBody LoginDto loginDto){
        return Result.success("登录成功", studentService.login(loginDto));
    }

    @GetMapping
    @Operation(summary = "获取个人信息")
    public Result<Student> selectById(){
        return Result.success(studentService.selectById());
    }

    @PutMapping
    @Operation(summary = "修改个人信息")
    public Result<Object> updateById(@RequestBody Student student){
        return Result.success("修改成功", studentService.updateById(student));
    }

    @PutMapping("/password")
    @Operation(summary = "修改密码")
    public Result<Object> updatePassword(@RequestBody PasswordDto dto){
        return Result.success("修改成功", studentService.updatePassword(dto));
    }

    @DeleteMapping
    @Operation(summary = "注销")
    public Result<Object> deleteById(){
        return Result.success("注销成功", studentService.deleteById());
    }

}
