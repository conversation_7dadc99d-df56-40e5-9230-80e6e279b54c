package org.zc.net.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.zc.net.common.Result;
import org.zc.net.entity.TagNode;
import org.zc.net.entity.TagRelationship;
import org.zc.net.entity.vo.TagVo;
import org.zc.net.service.impl.TagService;

@RestController
@RequestMapping("/tag")
@Tag(name = "TagController", description = "知识点管理Controller")
public class TagController {

    @Autowired
    TagService tagService;

    @GetMapping
    @Operation(summary = "获取知识图谱")
    public Result<TagVo> selectTag(@RequestParam("categoryId") Long categoryId) {
        return Result.success(tagService.selectTag(categoryId));
    }

    @Operation(summary = "获取知识点")
    @GetMapping("/{id}")
    public Result<TagNode> selectById(@PathVariable("id") Long id) {
        return Result.success(tagService.selectById(id));
    }

    @Operation(summary = "添加知识点")
    @PostMapping
    public Result<Object> insert(@RequestBody TagNode tag) {
        return Result.success("添加成功", tagService.insert(tag));
    }

    @Operation(summary = "修改知识点")
    @PutMapping
    public Result<Object> updateById(@RequestBody TagNode tag) {
        return Result.success("修改成功", tagService.updateById(tag));
    }

    @Operation(summary = "删除知识点")
    @DeleteMapping("/{id}")
    public Result<Object> deleteById(@PathVariable("id") Long id) {
        return Result.success("删除成功", tagService.deleteById(id));
    }

    @Operation(summary = "获取知识点关系")
    @GetMapping("/relationship/{id}")
    public Result<TagRelationship> selectRelationship(@PathVariable("id") Long id) {
        return Result.success(tagService.selectRelationship(id));
    }

    @Operation(summary = "添加知识点关系")
    @PostMapping("/relationship")
    public Result<Object> insertRelationship(@RequestBody TagRelationship relationship) {
        return Result.success("添加成功", tagService.insertRelationship(relationship));
    }

    @Operation(summary = "修改知识点关系")
    @PutMapping("/relationship")
    public Result<Object> updateRelationship(@RequestBody TagRelationship relationship) {
        return Result.success("修改成功", tagService.updateRelationship(relationship));
    }

    @Operation(summary = "删除知识点关系")
    @DeleteMapping("/relationship/{id}")
    public Result<Object> deleteRelationship(@PathVariable("id") Long id) {
        return Result.success("删除成功", tagService.deleteRelationship(id));
    }

}
