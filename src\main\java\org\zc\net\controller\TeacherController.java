package org.zc.net.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.zc.net.common.Result;
import org.zc.net.entity.Teacher;
import org.zc.net.entity.dto.LoginDto;
import org.zc.net.entity.dto.PasswordDto;
import org.zc.net.service.impl.TeacherService;

/**
 * 教师管理控制器
 * 
 * <AUTHOR>
 * @date 2023-05-15
 */
@RestController
@RequestMapping("/teacher")
@Tag(name = "TeacherController", description = "教师管理Controller")
public class TeacherController extends BaseController {

    @Autowired
    TeacherService teacherService;

    @PostMapping("/register")
    @Operation(summary = "注册")
    public Result<Object> register(@RequestBody LoginDto loginDto){
        return Result.success("注册成功", teacherService.register(loginDto));
    }

    @PostMapping("/login")
    @Operation(summary = "登录")
    public Result<String> login(@RequestBody LoginDto loginDto){
        return Result.success("登录成功", teacherService.login(loginDto));
    }

    @GetMapping
    @Operation(summary = "获取个人信息")
    public Result<Teacher> selectById(){
        return Result.success(teacherService.selectById());
    }

    @PutMapping
    @Operation(summary = "修改个人信息")
    public Result<Object> updateById(@RequestBody Teacher teacher){
        return Result.success("修改成功", teacherService.updateById(teacher));
    }

    @PutMapping("/password")
    @Operation(summary = "修改密码")
    public Result<Object> updatePassword(@RequestBody PasswordDto dto){
        return Result.success("修改成功", teacherService.updatePassword(dto));
    }

    @DeleteMapping
    @Operation(summary = "注销")
    public Result<Object> deleteById(){
        return Result.success("注销成功", teacherService.deleteById());
    }

}
