package org.zc.net.controller;

import org.springframework.web.bind.annotation.*;

/**
 * 测试控制器
 * 
 * <AUTHOR>
 * @date 2023-05-15
 */
@RestController
@RequestMapping("/test")
public class TestController extends BaseController {

    @GetMapping("/cors")
    public String testCors() {
        return "CORS配置正常！";
    }

    @PostMapping("/cors")
    public String testCorsPost(@RequestBody String data) {
        return "POST请求CORS配置正常！接收到的数据：" + data;
    }

    @RequestMapping(value = "/cors", method = RequestMethod.OPTIONS)
    public String testCorsOptions() {
        return "OPTIONS请求CORS配置正常！";
    }
} 