package org.zc.net.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
@Schema(title = "学生知识点")
public class StudentTag implements Serializable {

    @Schema(name = "ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(name = "学生ID")
    private Long studentId;

    @Schema(name = "知识点名称")
    private String tag;

    @Schema(name = "正确题数")
    private Long rightNum;

    @Schema(name = "总题数")
    private Long totalNum;

}
