package org.zc.net.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
@Schema(title = "知识点节点")
public class TagNode implements Serializable {

    @Schema(name = "知识点ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(name = "知识点名称")
    private String name;

    @Schema(name = "知识点层次", description = "课程/章/节/知识点")
    private String category;

    @Schema(name = "知识点描述")
    private String description;

    @Schema(name = "分组ID")
    private Long categoryId;

    @Schema(name = "教师ID")
    private Long teacherId;

}
