package org.zc.net.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Data
@Schema(title = "知识点关系")
public class TagRelationship implements Serializable {

    @Schema(name = "关系ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(name = "源节点ID")
    private Long source;

    @Schema(name = "目标节点ID")
    private Long target;

    @Schema(name = "关系名")
    private String name;

    @Schema(name = "分组ID")
    private Long categoryId;

    @Schema(name = "教师ID")
    private Long teacherId;

}
