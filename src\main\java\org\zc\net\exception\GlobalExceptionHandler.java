package org.zc.net.exception;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.BeanCreationException;
import org.springframework.context.ApplicationContextException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.zc.net.common.Code;
import org.zc.net.common.Result;

/**
 * 全局异常处理器
 * 
 * <AUTHOR>
 * @date 2023-05-15
 */
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    @ExceptionHandler(Exception.class)
    public Result<Object> handleException(Exception e) {
        logger.error("系统发生未处理的异常: {}", e.getMessage(), e);
        return Result.error(Code.INTERNAL_SERVER_ERROR, "系统错误: " + e.getMessage());
    }

    @ExceptionHandler(CustomException.class)
    public Result<Object> handleCustomException(CustomException e) {
        logger.error("自定义异常: {}", e.getMessage(), e);
        return Result.error(e.getCode(), e.getMsg());
    }
    
    @ExceptionHandler(BeanCreationException.class)
    public Result<Object> handleBeanCreationException(BeanCreationException e) {
        logger.error("Bean创建失败: {}", e.getMessage(), e);
        return Result.error(Code.INTERNAL_SERVER_ERROR, "配置错误: " + e.getMessage());
    }
    
    @ExceptionHandler(ApplicationContextException.class)
    public Result<Object> handleApplicationContextException(ApplicationContextException e) {
        logger.error("应用上下文异常: {}", e.getMessage(), e);
        return Result.error(Code.INTERNAL_SERVER_ERROR, "应用启动失败: " + e.getMessage());
    }
    
    @ExceptionHandler(IllegalArgumentException.class)
    public Result<Object> handleIllegalArgumentException(IllegalArgumentException e) {
        logger.error("参数错误: {}", e.getMessage(), e);
        return Result.error(Code.PARAM_ERROR, "参数错误: " + e.getMessage());
    }
    
    @ExceptionHandler(RuntimeException.class)
    public Result<Object> handleRuntimeException(RuntimeException e) {
        logger.error("运行时异常: {}", e.getMessage(), e);
        return Result.error(Code.INTERNAL_SERVER_ERROR, "运行时错误: " + e.getMessage());
    }

}
