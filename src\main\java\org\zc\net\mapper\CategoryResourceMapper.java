package org.zc.net.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.zc.net.entity.CategoryResource;
import org.zc.net.entity.Resource;

import java.util.List;

@Mapper
public interface CategoryResourceMapper extends BaseMapper<CategoryResource> {

    @Select("select r.* from category_resource cr left join resource r on cr.resource_id=r.id where cr.category_id=#{id}")
    List<Resource> selectResource(Long id);

}
