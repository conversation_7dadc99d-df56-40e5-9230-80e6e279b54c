package org.zc.net.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.zc.net.entity.Chat;

@Mapper
public interface ChatMapper extends BaseMapper<Chat> {

    @Select("select count(*) from chat where course_id=#{courseId} and user_id=#{studentId} and type='user'")
    Long selectChatCount(@Param("courseId") Long courseId, @Param("studentId") Long studentId);

}
