package org.zc.net.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.zc.net.entity.Course;
import org.zc.net.entity.CourseStudent;
import org.zc.net.entity.Student;

import java.util.List;

@Mapper
public interface CourseStudentMapper extends BaseMapper<CourseStudent> {

    @Select("select c.* from course_student cs left join course c on c.id=cs.course_id where cs.student_id=#{id} limit #{size} offset #{offset}")
    List<Course> selectCoursePage(@Param("id") Long id, @Param("offset") Long offset, @Param("size") Long size);

    List<Student> selectStudentPage(@Param("id") Long id, @Param("offset") Long offset, @Param("size") Long size, @Param("no") String no, @Param("name") String name);

    Long selectStudentCount(@Param("id") Long id, @Param("no") String no, @Param("name") String name);

}
