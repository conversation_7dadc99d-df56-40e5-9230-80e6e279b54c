package org.zc.net.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.zc.net.entity.StudentExam;
import org.zc.net.entity.dto.StatisticsDto;
import org.zc.net.entity.excel.ExamRankingExcel;
import org.zc.net.entity.vo.StudentExamVo;

import java.util.List;

@Mapper
public interface StudentExamMapper extends BaseMapper<StudentExam> {

    @Select("select s.id, no, name, score, start_time, end_time from student_exam se left join student s on se.student_id=s.id where exam_id=#{id} order by score desc limit #{size} offset #{offset}")
    List<StudentExamVo> selectRanking(@Param("id") Long id, @Param("size") Long size, @Param("offset") Long offset);

    @Select("select no, name, score, start_time, end_time from student_exam se left join student s on se.student_id=s.id where exam_id=#{id} order by score desc")
    List<ExamRankingExcel> selectExcelRanking(@Param("id") Long id);

    @Select("select s.id, no, name, score, start_time, end_time from student_exam se left join student s on se.student_id=s.id where exam_id=#{examId} and s.id=#{studentId} order by start_time desc")
    StudentExamVo selectExam(@Param("studentId") Long studentId, @Param("examId") Long examId);

    @Select("select max(score) max_score, min(score) min_score, avg(score) avg_score from student_exam where exam_id=#{id}")
    StatisticsDto selectStatistics(@Param("id") Long id);

    @Select("select count(*) from exam e left join student_exam se on se.exam_id=e.id where course_id=#{courseId} and student_id=#{studentId}")
    Long selectExamCount(@Param("courseId") Long courseId, @Param("studentId") Long studentId);

}
