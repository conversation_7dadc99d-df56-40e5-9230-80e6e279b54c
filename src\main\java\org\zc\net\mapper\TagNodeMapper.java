package org.zc.net.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.zc.net.entity.TagNode;
import org.zc.net.entity.vo.StudentTagNodeVo;

import java.util.List;

@Mapper
public interface TagNodeMapper extends BaseMapper<TagNode> {

    @Select("select t.id, t.name, t.description, round(s.right_num*100/s.total_num, 2) value, case when s.right_num*100/s.total_num < 60 then '薄弱' when s.right_num*100/s.total_num between 60 and 85 then '良好' when s.right_num*100/s.total_num > 85 then '优秀' end as category from student_tag s left join tag_node t on s.tag=t.name where category_id=#{id} and student_id=#{studentId}")
    List<StudentTagNodeVo> selectStudent(@Param("id") Long id, @Param("studentId") Long studentId);

}
