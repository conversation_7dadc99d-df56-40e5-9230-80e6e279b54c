package org.zc.net.service;

import org.zc.net.entity.Category;
import org.zc.net.entity.vo.CategoryVo;

import java.util.List;

/**
 * 分组 服务层
 *
 * <AUTHOR>
 */
public interface ICategoryService {

    /**
     * 获取指定ID分组信息
     *
     * @param categoryId 分组ID
     * @return 分组信息
     */
    Category selectById(Long categoryId);

    /**
     * 获取分组树列表
     *
     * @param type 分组类型
     * @return 分组树列表
     */
    List<CategoryVo> selectTree(String type);

    /**
     * 添加分组
     * @param category 分组信息
     * @return 添加结果
     */
    Integer insert(Category category);

    /**
     * 修改分组
     *
     * @param category 分组信息
     * @return 修改结果
     */
    Integer updateById(Category category);

    /**
     * 删除分组
     *
     * @param categoryId 分组ID
     * @return 删除结果
     */
    Integer deleteById(Long categoryId);

    /**
     * 在分组下添加资源
     *
     * @param categoryId 分组ID
     * @param resourceIds 资源ID列表
     * @return 添加结果
     */
    Integer insertResource(Long categoryId, Long[] resourceIds);

    /**
     * 删除分组下的资源
     *
     * @param categoryId 分组ID
     * @param resourceId 资源ID
     * @return 删除结果
     */
    Integer deleteResource(Long categoryId, Long resourceId);

}
