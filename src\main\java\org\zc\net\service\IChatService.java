package org.zc.net.service;

import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import org.zc.net.entity.Chat;
import org.zc.net.entity.dto.ChatDto;

import java.util.List;

/**
 * 对话 服务层
 *
 * <AUTHOR>
 */
public interface IChatService {

    String getCoze(ChatDto dto);

    String postCoze(Long examId, Long studentId);

    /**
     * 处理用户聊天消息，并将AI的回答以SSE形式发送回客户端，同时存储聊天记录。
     *
     * @param dto 用户发送的消息
     * @return SseEmitter 用于发送SSE事件的发射器
     */
    SseEmitter streamCoze(ChatDto dto);

    /**
     * 获取班课AI助教历史对话
     * @param courseId 班课ID
     * @return 历史对话
     */
    List<Chat> selectHistory(Long courseId);

    /**
     * 删除班课AI助教历史对话
     * @param courseId 班课ID
     * @return 删除结果
     */
    Integer deleteHistory(Long courseId);

}
