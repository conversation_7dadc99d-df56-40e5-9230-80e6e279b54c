package org.zc.net.service;

import jakarta.servlet.http.HttpServletResponse;
import org.zc.net.entity.*;
import org.zc.net.entity.dto.QuestionDto;
import org.zc.net.entity.vo.*;

import java.util.List;

/**
 * 班课 服务层
 *
 * <AUTHOR>
 */
public interface ICourseService {

    /**
     * 获取指定ID班课信息
     *
     * @param courseId 班课ID
     * @return 班课信息
     */
    CourseVo selectById(Long courseId);

    /**
     * 获取条件分页班课列表
     *
     * @param pageNum 页码
     * @param pageSize 页长
     * @param course 班课信息（查询参数）
     * @return 班课列表
     */
    PageVo<Course> selectPage(Long pageNum, Long pageSize, Course course);

    /**
     * 添加班课
     *
     * @param course 班课信息
     * @return 添加结果
     */
    Integer insert(Course course);

    /**
     * 修改班课
     *
     * @param course 班课信息
     * @return 修改结果
     */
    Integer updateById(Course course);

    /**
     * 删除班课
     *
     * @param courseId 班课ID
     * @return 删除结果
     */
    Integer deleteById(Long courseId);

    /**
     * 获取班课测验列表
     *
     * @param courseId 班课ID
     * @param pageNum 页码
     * @param pageSize 页长
     * @param exam 测验信息（查询参数）
     * @return 测验列表
     */
    PageVo<Exam> selectExamPage(Long courseId, Long pageNum, Long pageSize, Exam exam);

    /**
     * 获取班课资源列表
     *
     * @param courseId 班课ID
     * @return 资源列表
     */
    List<CategoryResourceVo> selectResource(Long courseId);

    /**
     * 获取班课学生列表
     *
     * @param courseId 班课ID
     * @param pageNum 页码
     * @param pageSize 页长
     * @param student 学生信息（查询参数）
     * @return 学生列表
     */
    PageVo<Student> selectStudentPage(Long courseId, Long pageNum, Long pageSize, Student student);

    /**
     * 添加班课学生
     *
     * @param courseId 班课ID
     * @param studentNos 学号列表
     * @return 添加结果
     */
    Integer insertStudent(Long courseId, String[] studentNos);

    /**
     * 删除班课学生
     *
     * @param courseId 班课ID
     * @param studentId 学生ID
     * @return 删除结果
     */
    Integer deleteStudent(Long courseId, Long studentId);

    /**
     * 导出班课学生
     *
     * @param response http响应对象
     * @param courseId 班课ID
     */
    void downloadStudent(HttpServletResponse response, Long courseId);

    /**
     * 获取班课知识图谱
     *
     * @param courseId 班课ID
     * @return 知识图谱
     */
    TagVo selectTag(Long courseId);

    /**
     * 获取班课学生知识点掌握情况
     *
     * @param courseId 班课ID
     * @param studentId 学生ID
     * @return 学生知识图谱掌握情况
     */
    StudentTagVo selectStudentTag(Long courseId, Long studentId);

    /**
     * 获取知识点资源
     *
     * @param courseId 班课ID
     * @param tagId 知识点节点ID
     * @return 资源列表
     */
    List<Resource> selectTagResource(Long courseId, Long tagId);

    /**
     * 获取知识点练习
     *
     * @param courseId 班课ID
     * @param tagId 知识点节点ID
     * @return 题目
     */
    Question selectPractice(Long courseId, Long tagId);

    /**
     * 提交知识点练习
     *
     * @param q 作答信息
     * @return 提交结果
     */
    Integer submitPractice(QuestionDto q);

}
