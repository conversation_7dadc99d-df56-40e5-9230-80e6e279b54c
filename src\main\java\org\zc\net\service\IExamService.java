package org.zc.net.service;

import jakarta.servlet.http.HttpServletResponse;
import org.zc.net.entity.Exam;
import org.zc.net.entity.dto.*;
import org.zc.net.entity.vo.*;

import java.util.List;

/**
 * 测验 服务层
 *
 * <AUTHOR>
 */
public interface IExamService {

    /**
     * 获取指定ID测验信息
     *
     * @param examId 测验ID
     * @return 测验信息
     */
    ExamVo selectById(Long examId);

    /**
     * 获取条件分页测验列表
     *
     * @param pageNum 页码
     * @param pageSize 页长
     * @param exam 测验信息（查询参数）
     * @return 测验列表
     */
    PageVo<Exam> selectPage(Long pageNum, Long pageSize, Exam exam);

    /**
     * 添加测验
     *
     * @param exam 测验信息
     * @return 添加结果
     */
    Integer insert(Exam exam);

    /**
     * 修改测验
     *
     * @param exam 测验信息
     * @return 修改结果
     */
    Integer updateById(Exam exam);

    /**
     * 删除测验
     *
     * @param examId 测验ID
     * @return 删除结果
     */
    Integer deleteById(Long examId);

    /**
     * 学生开始测验
     *
     * @param examId 测验ID
     * @return 试卷和收卷时间
     */
    StartExamVo startExam(Long examId);

    /**
     * 学生结束测验
     *
     * @param examId 测验ID
     * @param studentQuestions 学生答卷
     * @return 交卷结果
     */
    Integer endExam(Long examId, List<QuestionDto> studentQuestions);

    /**
     * 获取学生测验结果列表
     *
     * @param examId 测验ID
     * @param pageNum 页码
     * @param pageSize 页长
     * @return 测验结果列表
     */
    PageVo<StudentExamVo> selectStudentResultList(Long examId, Long pageNum, Long pageSize, String status);

    /**
     * 获取学生测验结果
     *
     * @param studentId 学生ID
     * @param examId 测验ID
     * @return 测验结果
     */
    StudentExamResultVo selectStudentResult(Long studentId, Long examId);

    /**
     * 修改学生测验结果
     *
     * @param studentId 学生ID
     * @param examId 测验ID
     * @param questions 测验结构
     * @return 修改结果
     */
    Integer editStudentResult(Long studentId, Long examId, List<QuestionDto> questions);

    /**
     * 导出学生答卷
     *
     * @param response http响应对象
     * @param examId 测验ID
     * @param studentId 学生ID
     */
    void downloadStudentPaper(HttpServletResponse response, Long examId, Long studentId);

    /**
     * 获取试卷题目
     *
     * @param examId 测验ID
     * @return 试卷题目列表
     */
    List<QuestionDto> selectQuestion(Long examId);

    /**
     * 添加试卷题目
     *
     * @param examId 测验ID
     * @param questionIds 题目ID
     * @return 添加结果
     */
    Integer insertQuestion(Long examId, Long[] questionIds);

    /**
     * 修改试卷题目
     *
     * @param questions 测验题目列表
     * @return 修改结果
     */
    Integer updateQuestion(Long examId, List<QuestionDto> questions) ;

    /**
     * 删除试卷题目
     *
     * @param examId 试卷题目ID
     * @return 删除结果
     */
    Integer deleteQuestion(Long examId, Long questionId);

    /**
     * 导出试卷题目
     *
     * @param response http响应对象
     * @param examId 测验ID
     */
    void downloadQuestion(HttpServletResponse response, Long examId);

    /**
     * 获取测验学生成绩排名
     *
     * @param examId 测验ID
     * @param pageNum 页码
     * @param pageSize 页长
     * @return 学生排名
     */
    PageVo<StudentExamVo> selectRanking(Long examId, Long pageNum, Long pageSize);

    /**
     * 导出学生成绩排名
     *
     * @param response http响应对象
     * @param examId 测验ID
     */
    void downloadRanking(HttpServletResponse response, Long examId);

    /**
     * 获取测验统计数据
     *
     * @param examId 测验ID
     * @return 测验统计数据
     */
    StatisticsDto selectStatistics(Long examId);
    
}
