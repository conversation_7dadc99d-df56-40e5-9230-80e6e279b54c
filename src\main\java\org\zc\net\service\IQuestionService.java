package org.zc.net.service;


import org.zc.net.entity.Question;
import org.zc.net.entity.dto.QuestionDto;
import org.zc.net.entity.vo.PageVo;

import java.util.List;

/**
 * 题目 服务层
 *
 * <AUTHOR>
 */
public interface IQuestionService {

    /**
     * 获取指定ID题目信息
     *
     * @param questionId 题目ID
     * @return 题目信息
     */
    Question selectById(Long questionId);

    /**
     * 获取条件分页题目列表
     *
     * @param pageNum 页码
     * @param pageSize 页长
     * @param question 题目信息（查询参数）
     * @return 题目列表
     */
    PageVo<Question> selectPage(Long pageNum, Long pageSize, Question question);

    /**
     * 添加题目
     *
     * @param question 题目信息
     * @return 添加结果
     */
    Integer insert(Question question);

    /**
     * 修改题目
     *
     * @param question 题目信息
     * @return 修改结果
     */
    Integer updateById(Question question);

    /**
     * 删除题目
     *
     * @param questionIds 题目ID列表
     * @return 删除结果
     */
    Integer deleteById(List<Long> questionIds);
    
}
