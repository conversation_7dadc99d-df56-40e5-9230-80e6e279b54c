package org.zc.net.service;

import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;
import org.zc.net.entity.Resource;
import org.zc.net.entity.vo.PageVo;

/**
 * 资源 服务层
 *
 * <AUTHOR>
 */
public interface IResourceService {

    /**
     * 获取指定ID资源信息
     *
     * @param resourceId 资源ID
     * @return 资源信息
     */
    Resource selectById(Long resourceId);

    /**
     * 获取条件分页资源列表
     *
     * @param pageNum 页码
     * @param pageSize 页长
     * @param resource 资源信息（查询参数）
     * @return 资源列表
     */
    PageVo<Resource> selectPage(Long pageNum, Long pageSize, Resource resource);

    /**
     * 上传资源
     *
     * @param file 资源文件
     * @return 上传结果
     */
    Resource upload(MultipartFile file);

    /**
     * 添加资源信息
     *
     * @param resource 资源信息
     * @return 添加结果
     */
    Integer insert(Resource resource);

    /**
     * 修改资源信息
     *
     * @param resource 资源信息
     * @return 修改结果
     */
    Integer updateById(Resource resource);

    /**
     * 下载资源
     *
     * @param response http响应对象
     * @param resourceId 资源ID
     */
    void download(HttpServletResponse response, Long resourceId);

    /**
     * 获取预览路径
     *
     * @param resourceId 资源ID
     * @return 预览路径
     */
    String getPreviewUrl(Long resourceId);

    /**
     * 删除资源
     *
     * @param resourceId 资源ID
     * @return 删除结果
     */
    Integer deleteById(Long resourceId);
    
}
