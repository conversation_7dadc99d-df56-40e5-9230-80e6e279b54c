package org.zc.net.service;

import org.zc.net.entity.Student;
import org.zc.net.entity.dto.LoginDto;
import org.zc.net.entity.dto.PasswordDto;

/**
 * 学生 服务层
 *
 * <AUTHOR>
 */
public interface IStudentService {

    /**
     * 注册
     *
     * @param loginDto 账号、密码
     * @return 注册结果
     */
    Integer register(LoginDto loginDto);

    /**
     * 登录
     *
     * @param loginDto 账号、密码
     * @return token
     */
    String login(LoginDto loginDto);

    /**
     * 获取用户个人信息
     *
     * @return 用户信息
     */
    Student selectById();

    /**
     * 修改用户个人信息
     *
     * @param student 用户信息
     * @return 修改结果
     */
    Integer updateById(Student student);

    /**
     * 修改密码
     *
     * @param dto 旧密码、新密码
     * @return 修改结果
     */
    Integer updatePassword(PasswordDto dto);

    /**
     * 注销
     *
     * @return 注销结果
     */
    Integer deleteById();

}
