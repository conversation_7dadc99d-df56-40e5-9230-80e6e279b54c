package org.zc.net.service;

import org.zc.net.entity.TagNode;
import org.zc.net.entity.TagRelationship;
import org.zc.net.entity.vo.TagVo;

/**
 * 知识点 服务层
 *
 * <AUTHOR>
 */
public interface ITagService {

    /**
     * 获取知识图谱
     *
     * @param categoryId 分组ID（查询参数）
     * @return 知识图谱
     */
    TagVo selectTag(Long categoryId);

    /**
     * 获取指定ID知识点节点信息
     *
     * @param id 知识点节点ID
     * @return 知识点节点信息
     */
    TagNode selectById(Long id);

    /**
     * 添加知识点节点
     *
     * @param tag 知识点节点信息
     * @return 添加结果
     */
    Integer insert(TagNode tag);

    /**
     * 修改知识点节点
     *
     * @param tag 知识点节点信息
     * @return 修改结果
     */
    Integer updateById(TagNode tag);

    /**
     * 删除知识点节点
     *
     * @param id 知识点节点ID
     * @return 删除结果
     */
    Integer deleteById(Long id);

    /**
     * 获取指定ID知识点关系信息
     *
     * @param id 知识点关系ID
     * @return 知识点关系信息
     */
    TagRelationship selectRelationship(Long id);

    /**
     * 添加知识点关系
     *
     * @param relationship 知识点关系信息
     * @return 添加结果
     */
    Integer insertRelationship(TagRelationship relationship);

    /**
     * 修改知识点关系
     *
     * @param relationship 知识点关系信息
     * @return 修改结果
     */
    Integer updateRelationship(TagRelationship relationship);

    /**
     * 删除知识点关系
     *
     * @param id 知识点关系ID
     * @return 删除结果
     */
    Integer deleteRelationship(Long id);

}
