package org.zc.net.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.zc.net.entity.Category;
import org.zc.net.entity.CategoryResource;
import org.zc.net.entity.vo.CategoryVo;
import org.zc.net.mapper.CategoryMapper;
import org.zc.net.mapper.CategoryResourceMapper;
import org.zc.net.service.ICategoryService;
import org.zc.net.util.JwtUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * 分组 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class CategoryService implements ICategoryService {

    @Autowired
    CategoryMapper categoryMapper;

    @Autowired
    CategoryResourceMapper categoryResourceMapper;

    /**
     * 获取指定ID分组信息
     *
     * @param categoryId 分组ID
     * @return 分组信息
     */
    @Override
    public Category selectById(Long categoryId) {
        return categoryMapper.selectById(categoryId);
    }

    /**
     * 获取分组树列表
     *
     * @param type 分组类型
     * @return 分组树列表
     */
    @Override
    public List<CategoryVo> selectTree(String type) {
        Long teacherId = JwtUtil.getUserId();

        // 查询分组
        List<Category> categories = categoryMapper.selectList(
                new LambdaQueryWrapper<Category>()
                        .eq(Category::getTeacherId, teacherId)
                        .eq(Category::getType, type));

        // 数据库中的结构是子节点指向父节点，需要转换成父节点包含子节点的结构
        List<CategoryVo> ans = new ArrayList<>(); // 第一层的节点集合
        List<CategoryVo> que = new ArrayList<>(); // 用于层序遍历的队列
        for(Category category : categories) { // 先把第一层的节点加入集合
            if(category.getParentId() == 0) {
                CategoryVo vo = new CategoryVo();
                vo.setId(category.getId());
                vo.setValue(category.getId());
                vo.setLabel(category.getName());
                vo.setChildren(new ArrayList<>());
                ans.add(vo);
                que.add(vo);
            }
        }
        for(int i = 0; i < que.size(); i ++) { // 再层序遍历，处理每一个节点包含有哪些子节点
            for(Category category : categories) {
                if(category.getParentId().equals(que.get(i).getId())) {
                    CategoryVo vo = new CategoryVo();
                    vo.setId(category.getId());
                    vo.setValue(category.getId());
                    vo.setLabel(category.getName());
                    vo.setChildren(new ArrayList<>());
                    que.get(i).getChildren().add(vo);
                    que.add(vo);
                }
            }
        }
        return ans;
    }

    /**
     * 添加分组
     * @param category 分组信息
     * @return 添加结果
     */
    @Override
    public Integer insert(Category category) {
        Long teacherId = JwtUtil.getUserId();
        category.setTeacherId(teacherId);
        // 添加分组
        return categoryMapper.insert(category);
    }

    /**
     * 修改分组
     *
     * @param category 分组信息
     * @return 修改结果
     */
    @Override
    public Integer updateById(Category category) {
        Long teacherId = JwtUtil.getUserId();
        category.setTeacherId(teacherId);
        // 修改分组
        return categoryMapper.updateById(category);
    }

    /**
     * 删除分组
     *
     * @param categoryId 分组ID
     * @return 删除结果
     */
    @Override
    public Integer deleteById(Long categoryId) {
        return categoryMapper.deleteById(categoryId);
    }

    /**
     * 在分组下添加资源
     *
     * @param categoryId 分组ID
     * @param resourceIds 资源ID列表
     * @return 添加结果
     */
    @Override
    public Integer insertResource(Long categoryId, Long[] resourceIds) {
        for(Long resourceId : resourceIds) {
            // 检查分组下是否已有该资源
            CategoryResource res = categoryResourceMapper.selectOne(
                    new LambdaQueryWrapper<CategoryResource>()
                            .eq(CategoryResource::getCategoryId, categoryId)
                            .eq(CategoryResource::getResourceId, resourceId));
            if(res == null) {
                // 添加分组资源
                CategoryResource cr = new CategoryResource();
                cr.setCategoryId(categoryId);
                cr.setResourceId(resourceId);
                int cnt = categoryResourceMapper.insert(cr);
                if(cnt == 0) return 0;
            }
        }
        return 1;
    }

    /**
     * 删除分组下的资源
     *
     * @param categoryId 分组ID
     * @param resourceId 资源ID
     * @return 删除结果
     */
    @Override
    public Integer deleteResource(Long categoryId, Long resourceId) {
        return categoryResourceMapper.delete(
                new LambdaQueryWrapper<CategoryResource>()
                        .eq(CategoryResource::getCategoryId, categoryId)
                        .eq(CategoryResource::getResourceId, resourceId));
    }

}
