package org.zc.net.service.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.zc.net.config.CozeConfig;
import org.zc.net.entity.*;
import org.zc.net.entity.dto.QuestionDto;
import org.zc.net.entity.vo.*;
import org.zc.net.entity.excel.CourseStudentExcel;
import org.zc.net.mapper.*;
import org.zc.net.service.ICourseService;
import org.zc.net.util.JwtUtil;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 班课 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class CourseService implements ICourseService {

    @Autowired
    CategoryMapper categoryMapper;

    @Autowired
    CategoryResourceMapper categoryResourceMapper;

    @Autowired
    ChatMapper chatMapper;

    @Autowired
    CourseMapper courseMapper;

    @Autowired
    CourseStudentMapper courseStudentMapper;

    @Autowired
    CozeConfig cozeConfig;

    @Autowired
    ExamMapper examMapper;

    @Autowired
    QuestionMapper questionMapper;

    @Autowired
    TeacherMapper teacherMapper;

    @Autowired
    StudentMapper studentMapper;

    @Autowired
    StudentExamMapper studentExamMapper;

    @Autowired
    StudentTagMapper studentTagMapper;

    @Autowired
    TagNodeMapper tagNodeMapper;

    @Autowired
    TagRelationshipMapper tagRelationshipMapper;

    @Autowired
    ResourceMapper resourceMapper;

    /**
     * 获取指定ID班课信息
     *
     * @param courseId 班课ID
     * @return 班课信息
     */
    @Override
    public CourseVo selectById(Long courseId) {
        Course course = courseMapper.selectById(courseId);
        Teacher teacher = teacherMapper.selectById(course.getTeacherId());
        CourseVo vo = new CourseVo();
        vo.setId(course.getId());
        vo.setName(course.getName());
        vo.setSemester(course.getSemester());
        vo.setClazz(course.getClazz());
        vo.setCover(course.getCover());
        vo.setTeacherId(course.getTeacherId());
        vo.setTeacherName(teacher.getName());
        vo.setTagCategoryId(course.getTagCategoryId());
        vo.setQuestionCategoryId(course.getQuestionCategoryId());
        return vo;
    }

    /**
     * 获取条件分页班课列表
     *
     * @param pageNum 页码
     * @param pageSize 页长
     * @param course 班课信息（查询参数）
     * @return 班课列表
     */
    @Override
    public PageVo<Course> selectPage(Long pageNum, Long pageSize, Course course) {
        Long userId = JwtUtil.getUserId();
        String role = JwtUtil.getUserRole();
        String name = course.getName();
        String semester = course.getSemester();

        PageVo<Course> vo = new PageVo<>();
        if(role.equals("teacher")) {

            LambdaQueryWrapper<Course> query = new LambdaQueryWrapper<>();
            query.eq(Course::getTeacherId, userId);
            if(name != null && !name.isEmpty()) {
                query.like(Course::getName, name);
            }
            if(semester != null && !semester.isEmpty()) {
                query.like(Course::getSemester, semester);
            }
            query.orderByDesc(Course::getCreateTime);
            Page<Course> page = courseMapper.selectPage(new Page<>(pageNum, pageSize), query);
            vo.setList(page.getRecords());
            vo.setTotal(page.getTotal());

        } else if(role.equals("student")) {

            List<Course> list = courseStudentMapper.selectCoursePage(userId, (pageNum - 1) * pageSize, pageSize);
            Long total = courseStudentMapper.selectCount(
                    new LambdaQueryWrapper<CourseStudent>()
                            .eq(CourseStudent::getStudentId, userId));
            vo.setList(list);
            vo.setTotal(total);

        }
        return vo;
    }

    /**
     * 添加班课
     *
     * @param course 班课信息
     * @return 添加结果
     */
    @Override
    public Integer insert(Course course) {
        Long teacherId = JwtUtil.getUserId();
        course.setTeacherId(teacherId);

        // 设置创建时间
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String createTime = LocalDateTime.now().format(formatter);
        course.setCreateTime(createTime);

        // 添加班课
        return courseMapper.insert(course);
    }

    /**
     * 修改班课
     *
     * @param course 班课信息
     * @return 修改结果
     */
    @Override
    public Integer updateById(Course course) {
        Long teacherId = JwtUtil.getUserId();
        course.setTeacherId(teacherId);

        // 设置修改时间
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String updateTime = LocalDateTime.now().format(formatter);
        course.setCreateTime(updateTime);

        // 修改班课
        return courseMapper.updateById(course);
    }

    /**
     * 删除班课
     *
     * @param courseId 班课ID
     * @return 删除结果
     */
    @Override
    public Integer deleteById(Long courseId) {
        return courseMapper.deleteById(courseId);
    }

    /**
     * 获取班课测验列表
     *
     * @param courseId 班课ID
     * @param pageNum 页码
     * @param pageSize 页长
     * @param exam 测验信息（查询参数）
     * @return 测验列表
     */
    @Override
    public PageVo<Exam> selectExamPage(Long courseId, Long pageNum, Long pageSize, Exam exam) {
        String name = exam.getName();

        // 拼接查询条件
        LambdaQueryWrapper<Exam> query = new LambdaQueryWrapper<>();
        query.eq(Exam::getCourseId, courseId);
        if(name != null && !name.isEmpty()) {
            query.like(Exam::getName, name);
        }
        query.orderByDesc(Exam::getCloseTime);

        // 执行查询
        Page<Exam> res = examMapper.selectPage(new Page<>(pageNum, pageSize), query);
        PageVo<Exam> vo = new PageVo<>();
        vo.setList(res.getRecords());
        vo.setTotal(res.getTotal());
        return vo;
    }

    /**
     * 获取班课资源列表
     *
     * @param courseId 班课ID
     * @return 资源列表
     */
    @Override
    public List<CategoryResourceVo> selectResource(Long courseId) {
        // 获取分组
        List<Category> categories = categoryMapper.selectList(
                new LambdaQueryWrapper<Category>()
                        .eq(Category::getCourseId, courseId));
        // 获取分组下的资源
        List<CategoryResourceVo> list = new ArrayList<>();
        for(Category category : categories) {
            List<Resource> resources = categoryResourceMapper.selectResource(category.getId());
            CategoryResourceVo vo = new CategoryResourceVo();
            vo.setId(category.getId());
            vo.setName(category.getName());
            vo.setChildren(resources);
            list.add(vo);
        }
        return list;
    }

    /**
     * 获取班课学生列表
     *
     * @param courseId 班课ID
     * @param pageNum 页码
     * @param pageSize 页长
     * @param student 学生信息（查询参数）
     * @return 学生列表
     */
    @Override
    public PageVo<Student> selectStudentPage(Long courseId, Long pageNum, Long pageSize, Student student) {
        String no = student.getNo();
        String name = student.getName();

        List<Student> list = courseStudentMapper.selectStudentPage(courseId, (pageNum-1)*pageSize, pageSize, no, name);
        Long total = courseStudentMapper.selectStudentCount(courseId, no, name);
        PageVo<Student> vo = new PageVo<>();
        vo.setList(list);
        vo.setTotal(total);
        return vo;
    }

    /**
     * 添加班课学生
     *
     * @param courseId 班课ID
     * @param studentNos 学号列表
     * @return 添加结果
     */
    @Override
    public Integer insertStudent(Long courseId, String[] studentNos) {
        // 检查学号是否都存在
        List<Long> studentIds = new ArrayList<>();
        for(String no : studentNos) {
            Student student = studentMapper.selectOne(
                    new LambdaQueryWrapper<Student>()
                            .eq(Student::getNo, no));
            if (student == null) return 0;
            studentIds.add(student.getId());
        }
        for(Long studentId : studentIds) {
            // 检查是否已经添加过了
            CourseStudent res = courseStudentMapper.selectOne(
                    new LambdaQueryWrapper<CourseStudent>()
                            .eq(CourseStudent::getCourseId, courseId)
                            .eq(CourseStudent::getStudentId, studentId));
            if(res == null) {
                CourseStudent cs = new CourseStudent();
                cs.setCourseId(courseId);
                cs.setStudentId(studentId);
                int cnt = courseStudentMapper.insert(cs);
                if(cnt == 0) return 0;
            }
        }
        return 1;
    }

    /**
     * 删除班课学生
     *
     * @param courseId 班课ID
     * @param studentId 学生ID
     * @return 删除结果
     */
    @Override
    public Integer deleteStudent(Long courseId, Long studentId) {
        return courseStudentMapper.delete(
                new LambdaQueryWrapper<CourseStudent>()
                        .eq(CourseStudent::getCourseId, courseId)
                        .eq(CourseStudent::getStudentId, studentId));
    }

    /**
     * 导出班课学生
     *
     * @param response http响应对象
     * @param courseId 班课ID
     */
    @Override
    public void downloadStudent(HttpServletResponse response, Long courseId) {
        Course course = courseMapper.selectById(courseId);
        String fileName = course.getName() + course.getSemester() + course.getClazz() + "学生名单.xlsx";
        List<CourseStudentExcel> list = new ArrayList<>();
        List<CourseStudent> students = courseStudentMapper.selectList(
                new LambdaQueryWrapper<CourseStudent>()
                        .eq(CourseStudent::getCourseId, courseId));
        for(CourseStudent cs : students) {
            CourseStudentExcel cse = new CourseStudentExcel();
            Long studentId = cs.getStudentId();
            Student student = studentMapper.selectById(studentId);
            Long examNum = studentExamMapper.selectExamCount(courseId, studentId);
            Long chatNum = chatMapper.selectChatCount(courseId, studentId);
            List<StudentTagNodeVo> studentTags = tagNodeMapper.selectStudent(course.getTagCategoryId(), studentId);
            Long tagNum = (long) studentTags.size();

            cse.setCourseName(course.getName());
            cse.setSemester(course.getSemester());
            cse.setClazz(cse.getClazz());
            cse.setNo(student.getNo());
            cse.setStudentName(student.getName());
            cse.setExamNum(examNum);
            cse.setChatNum(chatNum);
            cse.setTagNum(tagNum);

            list.add(cse);
        }
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition"); // 将Content-Disposition属性暴露给浏览器
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
        try {
            EasyExcel.write(response.getOutputStream(), CourseStudentExcel.class).autoCloseStream(Boolean.FALSE).sheet("学生").doWrite(list);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取班课知识图谱
     *
     * @param courseId 班课ID
     * @return 知识图谱
     */
    @Override
    public TagVo selectTag(Long courseId) {
        Course course = courseMapper.selectById(courseId);
        List<TagNode> nodes = tagNodeMapper.selectList(
                new LambdaQueryWrapper<TagNode>()
                        .eq(TagNode::getCategoryId, course.getTagCategoryId()));
        List<TagRelationship> links = tagRelationshipMapper.selectList(
                new LambdaQueryWrapper<TagRelationship>()
                        .eq(TagRelationship::getCategoryId, course.getTagCategoryId()));
        TagVo vo = new TagVo();
        vo.setNodes(nodes);
        vo.setLinks(links);
        return vo;
    }

    /**
     * 获取班课学生知识点掌握情况
     *
     * @param courseId 班课ID
     * @param studentId 学生ID
     * @return 学生知识图谱掌握情况
     */
    @Override
    public StudentTagVo selectStudentTag(Long courseId, Long studentId) {
        Course course = courseMapper.selectById(courseId);
        List<TagNode> allNodes = tagNodeMapper.selectList(
                new LambdaQueryWrapper<TagNode>()
                        .eq(TagNode::getCategoryId, course.getTagCategoryId()));
        List<StudentTagNodeVo> studentNodes = tagNodeMapper.selectStudent(course.getTagCategoryId(), studentId);
        List<TagRelationship> links = tagRelationshipMapper.selectList(
                new LambdaQueryWrapper<TagRelationship>()
                        .eq(TagRelationship::getCategoryId, course.getTagCategoryId()));

        Map<Long, TagNode> allNodesMap = new HashMap<>();
        for(TagNode node : allNodes) {
            allNodesMap.put(node.getId(), node);
        }
        Map<Long, StudentTagNodeVo> studentNodesMap = new HashMap<>();
        Map<Long, Boolean> vis = new HashMap<>();
        for(StudentTagNodeVo node : studentNodes) {
            studentNodesMap.put(node.getId(), node);
            vis.put(node.getId(), true);
        }
        for(TagRelationship link : links) {
            if(studentNodesMap.get(link.getSource()) != null
                    && studentNodesMap.get(link.getTarget()) == null
                    && vis.get(link.getTarget()) == null) {
                vis.put(link.getTarget(), true);
                TagNode targetNode = allNodesMap.get(link.getTarget());
                StudentTagNodeVo node = new StudentTagNodeVo();
                node.setId(targetNode.getId());
                node.setName(targetNode.getName());
                node.setCategory("未学");
                node.setDescription(targetNode.getDescription());
                node.setValue(0.0);
                studentNodes.add(node);
            }
        }

        StudentTagVo vo = new StudentTagVo();
        vo.setNodes(studentNodes);
        vo.setLinks(links);
        return vo;
    }

    /**
     * 获取知识点资源
     *
     * @param courseId 班课ID
     * @param tagId 知识点节点ID
     * @return 资源列表
     */
    @Override
    public List<Resource> selectTagResource(Long courseId, Long tagId) {
        TagNode tagNode = tagNodeMapper.selectById(tagId);
        List<CategoryResourceVo> list = selectResource(courseId);
        List<Resource> ans = new ArrayList<>();
        for(CategoryResourceVo vo : list) {
            List<Resource> children = vo.getChildren();
            for(Resource resource : children) {
                if(resource.getName().contains(tagNode.getName())) {
                    ans.add(resource);
                    continue;
                }
                Resource res = resourceMapper.selectById(resource.getId());
                List<String> tags = res.getTags();
                for(String tag : tags) {
                    if(tag.contains(tagNode.getName())) {
                        ans.add(resource);
                        break;
                    }
                }
            }
        }
        return ans;
    }

    /**
     * 获取知识点练习
     *
     * @param courseId 班课ID
     * @param tagId 知识点节点ID
     * @return 题目
     */
    @Override
    public Question selectPractice(Long courseId, Long tagId) {
        Long studentId = JwtUtil.getUserId();
        TagNode tagNode = tagNodeMapper.selectById(tagId);
        Course course = courseMapper.selectById(courseId);
        StudentTag studentTag = studentTagMapper.selectOne(
                new LambdaQueryWrapper<StudentTag>()
                        .eq(StudentTag::getStudentId, studentId)
                        .eq(StudentTag::getTag, tagNode.getName()));
        LambdaQueryWrapper<Question> query = new LambdaQueryWrapper<Question>()
                .eq(Question::getCategoryId, course.getQuestionCategoryId())
                .ne(Question::getType, "综合题")
                .like(Question::getTags, "\"" + tagNode.getName() + "\"");
        double mastery = 0.0;
        if(studentTag != null) {
            mastery = studentTag.getRightNum() * 1.0 / studentTag.getTotalNum();
        }
        if(mastery < 60) {
            query.eq(Question::getDifficulty, "简单");
        } else if(mastery < 85) {
            query.eq(Question::getDifficulty, "中等");
        } else {
            query.eq(Question::getDifficulty, "困难");
        }
        List<Question> questions = questionMapper.selectList(query);
        int randomNumber = (int) (Math.random() * questions.size());
        Question question = new Question();
        if(!questions.isEmpty()) {
            question = questions.get(randomNumber);
        } else {
            // JSONObject c = cozeConfig.makePractice(tagNode.getName(), mastery);
            // question.setContent(c.getStr("content"));
            // question.setType(c.getStr("type"));
            // question.setAnswer(c.getStr("answer"));
            // JSONArray options = JSONUtil.parseArray(c.getStr("options"));
            // question.setOptions(options.toList(String.class));
            // JSONArray tags = JSONUtil.parseArray(c.getStr("tags"));
            // question.setTags(tags.toList(String.class));
            // System.out.println(question);
            
            // 设置默认题目内容
            question.setContent("AI服务暂时不可用，请稍后再试");
            question.setType("单选题");
            question.setAnswer("A");
            question.setOptions(Arrays.asList("AI服务暂时不可用", "请稍后再试", "请联系管理员", "系统维护中"));
            question.setTags(Arrays.asList(tagNode.getName()));
        }
        return question;
    }

    /**
     * 提交知识点练习
     *
     * @param q 作答信息
     * @return 提交结果
     */
    @Override
    public Integer submitPractice(QuestionDto q) {
        Long studentId = JwtUtil.getUserId();

        // 计算本题得分
        Long rightNum = 0L;
        if(q.getAnswer().equals(q.getStudentAnswer())) {
            rightNum = 1L;
        }

        // 记录知识点正确率
        List<String> tags = q.getTags();
        for(String tag : tags) {
            StudentTag st = studentTagMapper.selectOne(
                    new LambdaQueryWrapper<StudentTag>()
                            .eq(StudentTag::getStudentId, studentId)
                            .eq(StudentTag::getTag, tag));
            if(st == null) {
                st = new StudentTag();
                st.setStudentId(studentId);
                st.setTag(tag);
                st.setRightNum(rightNum);
                st.setTotalNum(1L);
                studentTagMapper.insert(st);
            } else {
                st.setRightNum(st.getRightNum() + rightNum);
                st.setTotalNum(st.getTotalNum() + 1);
                studentTagMapper.updateById(st);
            }
        }

        return 1;
    }

}
