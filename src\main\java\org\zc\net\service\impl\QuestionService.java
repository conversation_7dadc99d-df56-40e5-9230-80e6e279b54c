package org.zc.net.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.zc.net.entity.Question;
import org.zc.net.entity.vo.PageVo;
import org.zc.net.mapper.QuestionMapper;
import org.zc.net.service.IQuestionService;
import org.zc.net.util.JwtUtil;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 题目 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class QuestionService implements IQuestionService {

    @Autowired
    QuestionMapper questionMapper;

    /**
     * 获取指定ID题目信息
     *
     * @param questionId 题目ID
     * @return 题目信息
     */
    @Override
    public Question selectById(Long questionId) {
        return questionMapper.selectById(questionId);
    }

    /**
     * 获取条件分页题目列表
     *
     * @param pageNum 页码
     * @param pageSize 页长
     * @param question 题目信息（查询参数）
     * @return 题目列表
     */
    @Override
    public PageVo<Question> selectPage(Long pageNum, Long pageSize, Question question) {
        // 获取查询条件
        Long teacherId = JwtUtil.getUserId();
        String content = question.getContent();
        Long categoryId = question.getCategoryId();
        String difficulty = question.getDifficulty();
        String type = question.getType();

        // 拼接查询条件
        LambdaQueryWrapper<Question> query = new LambdaQueryWrapper<>();
        query.eq(Question::getTeacherId, teacherId);
        if(content != null && !content.isEmpty()) {
            query.like(Question::getContent, content);
        }
        if(categoryId != null) {
            query.eq(Question::getCategoryId, categoryId);
        }
        if(difficulty != null && !difficulty.isEmpty()) {
            query.eq(Question::getDifficulty, difficulty);
        }
        if(type != null && !type.isEmpty()) {
            query.eq(Question::getType, type);
        }
        query.orderByDesc(Question::getCreateTime);

        // 执行查询
        Page<Question> res = questionMapper.selectPage(new Page<>(pageNum, pageSize), query);

        // 返回查询结果
        PageVo<Question> vo = new PageVo<>();
        vo.setTotal(res.getTotal());
        vo.setList(res.getRecords());
        return vo;
    }

    /**
     * 添加题目
     *
     * @param question 题目信息
     * @return 添加结果
     */
    @Override
    public Integer insert(Question question) {
        Long teacherId = JwtUtil.getUserId();
        question.setTeacherId(teacherId);

        // 设置创建时间
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String createTime = LocalDateTime.now().format(formatter);
        question.setCreateTime(createTime);

        // 添加题目
        return questionMapper.insert(question);
    }

    /**
     * 修改题目
     *
     * @param question 题目信息
     * @return 修改结果
     */
    @Override
    public Integer updateById(Question question) {
        Long teacherId = JwtUtil.getUserId();
        question.setTeacherId(teacherId);

        // 设置修改时间
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String updateTime = LocalDateTime.now().format(formatter);
        question.setCreateTime(updateTime);

        // 修改题目
        return questionMapper.updateById(question);
    }

    /**
     * 删除题目
     *
     * @param questionIds 题目ID列表
     * @return 删除结果
     */
    @Override
    public Integer deleteById(List<Long> questionIds) {
        return questionMapper.deleteBatchIds(questionIds);
    }

}
