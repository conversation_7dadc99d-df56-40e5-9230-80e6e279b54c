package org.zc.net.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.minio.*;
import io.minio.http.Method;

import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.compress.utils.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.zc.net.entity.Resource;
import org.zc.net.entity.vo.PageVo;
import org.zc.net.mapper.ResourceMapper;
import org.zc.net.service.IResourceService;
import org.zc.net.util.EncodingUtil;
import org.zc.net.util.JwtUtil;

import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

/**
 * 资源 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class ResourceService implements IResourceService {

    @Autowired
    ResourceMapper resourceMapper;

    @Autowired
    MinioClient minioClient;

    @Value("${minio.bucket:net-edu}")
    private String bucket;

    @Value("${kkfileview.url:http://localhost:8012}")
    private String kkfileviewUrl;

    /**
     * 获取指定ID资源信息
     *
     * @param resourceId 资源ID
     * @return 资源信息
     */
    @Override
    public Resource selectById(Long resourceId) {
        return resourceMapper.selectById(resourceId);
    }

    /**
     * 获取条件分页资源列表
     *
     * @param pageNum 页码
     * @param pageSize 页长
     * @param resource 资源信息（查询参数）
     * @return 资源列表
     */
    @Override
    public PageVo<Resource> selectPage(Long pageNum, Long pageSize, Resource resource) {
        Long teacherId = JwtUtil.getUserId();
        String name = resource.getName();
        Long categoryId = resource.getCategoryId();

        // 拼接查询条件
        LambdaQueryWrapper<Resource> query = new LambdaQueryWrapper<>();
        query.eq(Resource::getTeacherId, teacherId);
        if(name != null && !name.isEmpty()) {
            query.like(Resource::getName, name);
        }
        if(categoryId != null) {
            query.eq(Resource::getCategoryId, categoryId);
        }
        query.orderByAsc(Resource::getCreateTime);

        // 执行查询
        Page<Resource> res = resourceMapper.selectPage(new Page<>(pageNum, pageSize), query);
        PageVo<Resource> vo = new PageVo<>();
        vo.setList(res.getRecords().reversed());
        vo.setTotal(res.getTotal());
        return vo;
    }

    /**
     * 上传资源
     *
     * @param file 资源文件
     * @return 上传结果
     */
    @Override
    public Resource upload(MultipartFile file) {
        if(file == null){
            return null;
        }
        String originalFilename = file.getOriginalFilename();
        String newFilename = UUID.randomUUID().toString() + "_" + originalFilename;
        Resource resource = new Resource();
        resource.setName(originalFilename);
        resource.setUrl(newFilename);
        try{
            // 文件上传
            InputStream in = file.getInputStream();
            minioClient.putObject(
                    PutObjectArgs.builder().bucket(bucket).object(newFilename)
                            .stream(in, file.getSize(), -1)
                            .contentType(file.getContentType())
                            .build());
            in.close();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return resource;
    }

    /**
     * 添加资源信息
     *
     * @param resource 资源信息
     * @return 添加结果
     */
    @Override
    public Integer insert(Resource resource) {
        // 获取用户ID
        Long teacherId = JwtUtil.getUserId();
        resource.setTeacherId(teacherId);

        // 设置创建时间
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String createTime = LocalDateTime.now().format(formatter);
        resource.setCreateTime(createTime);

        // 添加资源
        return resourceMapper.insert(resource);
    }

    /**
     * 修改资源信息
     *
     * @param resource 资源信息
     * @return 修改结果
     */
    @Override
    public Integer updateById(Resource resource) {
        // 获取用户ID
        Long teacherId = JwtUtil.getUserId();
        resource.setTeacherId(teacherId);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String updateTime = LocalDateTime.now().format(formatter);
        resource.setUpdateTime(updateTime);

        // 修改资源
        return resourceMapper.updateById(resource);
    }

    /**
     * 下载资源
     *
     * @param response http响应对象
     * @param resourceId 资源ID
     */
    @Override
    public void download(HttpServletResponse response, Long resourceId) {
        Resource resource = resourceMapper.selectById(resourceId);
        String fileName = resource.getUrl();
        String originalFilename = resource.getName();
        InputStream in = null;
        try{
            // 获取对象信息
            StatObjectResponse stat = minioClient.statObject(
                    StatObjectArgs.builder().bucket(bucket).object(fileName).build());
            response.setContentType(stat.contentType() + ";charset=utf-8");
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition"); // 将Content-Disposition属性暴露给浏览器
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(originalFilename, StandardCharsets.UTF_8));
            // 文件下载
            in = minioClient.getObject(
                    GetObjectArgs.builder().bucket(bucket).object(fileName).build());
            IOUtils.copy(in, response.getOutputStream());
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            if(in != null){
                try{
                    in.close();
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
        }
    }

    /**
     * 获取预览路径
     *
     * @param resourceId 资源ID
     * @return 预览路径
     */
    @Override
    public String getPreviewUrl(Long resourceId) {
        Resource resource = resourceMapper.selectById(resourceId);
        String url = null;
        if(resource != null) {
            String fileName = resource.getUrl();
            try {
                url = minioClient.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder()
                        .bucket(bucket)
                        .object(fileName)
                        .method(Method.GET)
                        .build());
            } catch (Exception e) {
                e.printStackTrace();
            }
            try {
                url = EncodingUtil.base64AndUrlEncode(url);
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
            url = kkfileviewUrl + url;
        }
        return url;
    }

    /**
     * 删除资源
     *
     * @param resourceId 资源ID
     * @return 删除结果
     */
    @Override
    public Integer deleteById(Long resourceId) {
        return resourceMapper.deleteById(resourceId);
    }

}
