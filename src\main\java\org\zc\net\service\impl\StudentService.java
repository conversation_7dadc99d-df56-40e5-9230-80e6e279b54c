package org.zc.net.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.zc.net.common.Code;
import org.zc.net.entity.Student;
import org.zc.net.entity.dto.LoginDto;
import org.zc.net.entity.dto.PasswordDto;
import org.zc.net.exception.CustomException;
import org.zc.net.mapper.StudentMapper;
import org.zc.net.service.IStudentService;
import org.zc.net.util.CryptUtil;
import org.zc.net.util.JwtUtil;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 学生 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class StudentService implements IStudentService {

    @Autowired
    StudentMapper studentMapper;

    /**
     * 注册
     *
     * @param loginDto 账号、密码
     * @return 注册结果
     */
    @SneakyThrows
    @Override
    public Integer register(LoginDto loginDto) {
        // 检查账号是否已经被注册
        Student res = studentMapper.selectOne(
                new LambdaQueryWrapper<Student>()
                        .eq(Student::getNo, loginDto.getUsername()));
        if(res != null) {
            throw new CustomException(Code.UNAUTHORIZED, "用户名已被注册");
        }

        Student student = new Student();
        student.setNo(loginDto.getUsername());
        student.setPassword(CryptUtil.encrypt(loginDto.getPassword()));
        // 设置创建时间
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String createTime = LocalDateTime.now().format(formatter);
        student.setCreateTime(createTime);
        return studentMapper.insert(student);
    }

    /**
     * 登录
     *
     * @param loginDto 账号、密码
     * @return token
     */
    @SneakyThrows
    @Override
    public String login(LoginDto loginDto) {
        // 检查账号是否存在
        Student student = studentMapper.selectOne(
                new LambdaQueryWrapper<Student>()
                        .eq(Student::getNo, loginDto.getUsername()));

        // 检查密码是否正确
        if(student != null && CryptUtil.decrypt(loginDto.getPassword(), student.getPassword())) {
            return JwtUtil.createToken(student.getId(), "student");
        }
        throw new CustomException(Code.UNAUTHORIZED, "用户名或密码错误");
    }

    /**
     * 获取用户个人信息
     *
     * @return 用户信息
     */
    @Override
    public Student selectById() {
        // 获取用户ID
        Long userId = JwtUtil.getUserId();

        // 查询用户信息
        Student student = studentMapper.selectOne(
                new LambdaQueryWrapper<Student>()
                        .eq(Student::getId, userId));
        student.setPassword(null);
        return student;
    }

    /**
     * 修改用户个人信息
     *
     * @param student 用户信息
     * @return 修改结果
     */
    @Override
    public Integer updateById(Student student) {
        Long userId = JwtUtil.getUserId();
        student.setId(userId);
        // 不修改密码
        student.setPassword(null);
        // 设置修改时间
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String updateTime = LocalDateTime.now().format(formatter);
        student.setUpdateTime(updateTime);
        // 修改个人信息
        return studentMapper.updateById(student);
    }

    /**
     * 修改密码
     *
     * @param dto 旧密码、新密码
     * @return 修改结果
     */
    @Override
    public Integer updatePassword(PasswordDto dto) {
        Long userId = JwtUtil.getUserId();
        Student student = studentMapper.selectById(userId);
        // 检查旧密码是否正确
        String dbPassword = student.getPassword();
        if(CryptUtil.decrypt(dto.getOldPassword(), dbPassword)) {
            student.setPassword(CryptUtil.encrypt(dto.getNewPassword()));
            // 设置修改时间
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String updateTime = LocalDateTime.now().format(formatter);
            student.setUpdateTime(updateTime);
            return studentMapper.updateById(student);
        }
        return 0;
    }

    /**
     * 注销
     *
     * @return 注销结果
     */
    @Override
    public Integer deleteById() {
        Long userId = JwtUtil.getUserId();
        return studentMapper.deleteById(userId);
    }

}
