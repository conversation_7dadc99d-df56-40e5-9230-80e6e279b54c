package org.zc.net.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.zc.net.entity.TagNode;
import org.zc.net.entity.TagRelationship;
import org.zc.net.entity.vo.TagVo;
import org.zc.net.mapper.TagNodeMapper;
import org.zc.net.mapper.TagRelationshipMapper;
import org.zc.net.service.ITagService;
import org.zc.net.util.JwtUtil;

import java.util.List;

/**
 * 知识点 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class TagService implements ITagService {

    @Autowired
    TagNodeMapper tagNodeMapper;

    @Autowired
    TagRelationshipMapper tagRelationshipMapper;

    /**
     * 获取知识图谱
     *
     * @param categoryId 分组ID（查询参数）
     * @return 知识图谱
     */
    @Override
    public TagVo selectTag(Long categoryId) {
        Long userId = JwtUtil.getUserId();

        List<TagNode> nodes = tagNodeMapper.selectList(
                new LambdaQueryWrapper<TagNode>()
                        .eq(TagNode::getTeacherId, userId)
                        .eq(TagNode::getCategoryId, categoryId));
        List<TagRelationship> links = tagRelationshipMapper.selectList(
                new LambdaQueryWrapper<TagRelationship>()
                        .eq(TagRelationship::getTeacherId, userId)
                        .eq(TagRelationship::getCategoryId, categoryId));
        TagVo vo = new TagVo();
        vo.setNodes(nodes);
        vo.setLinks(links);
        return vo;
    }

    /**
     * 获取指定ID知识点节点信息
     *
     * @param id 知识点节点ID
     * @return 知识点节点信息
     */
    @Override
    public TagNode selectById(Long id) {
        return tagNodeMapper.selectById(id);
    }

    /**
     * 添加知识点节点
     *
     * @param tag 知识点节点信息
     * @return 添加结果
     */
    @Override
    public Integer insert(TagNode tag) {
        // 获取用户ID
        Long userId = JwtUtil.getUserId();
        tag.setTeacherId(userId);

        // 添加知识点节点
        return tagNodeMapper.insert(tag);
    }

    /**
     * 修改知识点节点
     *
     * @param tag 知识点节点信息
     * @return 修改结果
     */
    @Override
    public Integer updateById(TagNode tag) {
        // 获取用户ID
        Long userId = JwtUtil.getUserId();
        tag.setTeacherId(userId);

        // 修改知识点节点
        return tagNodeMapper.updateById(tag);
    }

    /**
     * 删除知识点节点
     *
     * @param id 知识点节点ID
     * @return 删除结果
     */
    @Override
    public Integer deleteById(Long id) {
        return tagNodeMapper.deleteById(id);
    }

    /**
     * 获取指定ID知识点关系信息
     *
     * @param id 知识点关系ID
     * @return 知识点关系信息
     */
    @Override
    public TagRelationship selectRelationship(Long id) {
        return tagRelationshipMapper.selectById(id);
    }

    /**
     * 添加知识点关系
     *
     * @param relationship 知识点关系信息
     * @return 添加结果
     */
    @Override
    public Integer insertRelationship(TagRelationship relationship) {
        // 获取用户ID
        Long userId = JwtUtil.getUserId();
        relationship.setTeacherId(userId);

        // 添加知识点关系
        return tagRelationshipMapper.insert(relationship);
    }

    /**
     * 修改知识点关系
     *
     * @param relationship 知识点关系信息
     * @return 修改结果
     */
    @Override
    public Integer updateRelationship(TagRelationship relationship) {
        // 获取用户ID
        Long userId = JwtUtil.getUserId();
        relationship.setTeacherId(userId);

        // 修改知识点关系
        return tagRelationshipMapper.updateById(relationship);
    }

    /**
     * 删除知识点关系
     *
     * @param id 知识点关系ID
     * @return 删除结果
     */
    @Override
    public Integer deleteRelationship(Long id) {
        return tagRelationshipMapper.deleteById(id);
    }

}
