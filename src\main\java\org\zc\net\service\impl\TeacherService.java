package org.zc.net.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.zc.net.common.Code;
import org.zc.net.entity.Teacher;
import org.zc.net.entity.dto.LoginDto;
import org.zc.net.entity.dto.PasswordDto;
import org.zc.net.exception.CustomException;
import org.zc.net.mapper.TeacherMapper;
import org.zc.net.service.ITeacherService;
import org.zc.net.util.CryptUtil;
import org.zc.net.util.JwtUtil;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 教师 服务层实现
 *
 * <AUTHOR>
 */
@Service
public class TeacherService implements ITeacherService {

    @Autowired
    TeacherMapper teacherMapper;

    /**
     * 注册
     *
     * @param loginDto 账号、密码
     * @return 注册结果
     */
    @SneakyThrows
    @Override
    public Integer register(LoginDto loginDto) {
        // 检查账号是否已经被注册
        Teacher res = teacherMapper.selectOne(
                new LambdaQueryWrapper<Teacher>()
                        .eq(Teacher::getNo, loginDto.getUsername()));
        if(res != null) {
            throw new CustomException(Code.UNAUTHORIZED, "用户名已被注册");
        }

        Teacher teacher = new Teacher();
        teacher.setNo(loginDto.getUsername());
        teacher.setPassword(CryptUtil.encrypt(loginDto.getPassword()));
        // 设置创建时间
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String createTime = LocalDateTime.now().format(formatter);
        teacher.setCreateTime(createTime);
        return teacherMapper.insert(teacher);
    }

    /**
     * 登录
     *
     * @param loginDto 账号、密码
     * @return token
     */
    @SneakyThrows
    @Override
    public String login(LoginDto loginDto) {
        // 检查账号是否存在
        Teacher teacher = teacherMapper.selectOne(
                new LambdaQueryWrapper<Teacher>()
                        .eq(Teacher::getNo, loginDto.getUsername()));

        // 检查密码是否正确
        if(teacher != null && CryptUtil.decrypt(loginDto.getPassword(), teacher.getPassword())) {
            return JwtUtil.createToken(teacher.getId(), "teacher");
        }
        throw new CustomException(Code.UNAUTHORIZED, "用户名或密码错误");
    }

    /**
     * 获取用户个人信息
     *
     * @return 用户信息
     */
    @Override
    public Teacher selectById() {
        // 获取用户ID
        Long userId = JwtUtil.getUserId();

        // 查询用户信息
        Teacher teacher = teacherMapper.selectOne(
                new LambdaQueryWrapper<Teacher>()
                        .eq(Teacher::getId, userId));
        teacher.setPassword(null);
        return teacher;
    }

    /**
     * 修改用户个人信息
     *
     * @param teacher 用户信息
     * @return 修改结果
     */
    @Override
    public Integer updateById(Teacher teacher) {
        Long userId = JwtUtil.getUserId();
        teacher.setId(userId);
        // 不修改密码
        teacher.setPassword(null);
        // 设置修改时间
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String updateTime = LocalDateTime.now().format(formatter);
        teacher.setUpdateTime(updateTime);
        // 修改个人信息
        return teacherMapper.updateById(teacher);
    }

    /**
     * 修改密码
     *
     * @param dto 旧密码、新密码
     * @return 修改结果
     */
    @Override
    public Integer updatePassword(PasswordDto dto) {
        Long userId = JwtUtil.getUserId();
        Teacher teacher = teacherMapper.selectById(userId);
        // 检查旧密码是否正确
        String dbPassword = teacher.getPassword();
        if(CryptUtil.decrypt(dto.getOldPassword(), dbPassword)) {
            teacher.setPassword(CryptUtil.encrypt(dto.getNewPassword()));
            // 设置修改时间
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            String updateTime = LocalDateTime.now().format(formatter);
            teacher.setUpdateTime(updateTime);
            return teacherMapper.updateById(teacher);
        }
        return 0;
    }

    /**
     * 注销
     *
     * @return 注销结果
     */
    @Override
    public Integer deleteById() {
        Long userId = JwtUtil.getUserId();
        return teacherMapper.deleteById(userId);
    }
    
}
