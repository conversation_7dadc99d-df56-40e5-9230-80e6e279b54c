package org.zc.net.util;

import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;

import java.nio.charset.StandardCharsets;
import java.util.UUID;

/**
 * 加密工具类
 */
public class CryptUtil {

    /**
     * 加盐加密
     *
     * @param password 初始密码
     * @return 加盐密码
     */
    public static String encrypt(String password) {
        // 生成随机盐值
        String salt = UUID.randomUUID().toString().replace("-", "");
        return encrypt(password, salt);
    }

    /**
     * 加盐加密
     *
     * @param password 初始密码
     * @param salt 盐值
     * @return 加盐密码
     */
    public static String encrypt(String password, String salt) {
        // 根据初始密码 和 随机盐值 通过md5生成 加盐加密的密码
        String finalPassword = DigestUtils.md5DigestAsHex((salt + password).getBytes(StandardCharsets.UTF_8));
        // 合并 盐值 和 加盐密码
        return salt + "$" + finalPassword;
    }

    /**
     * 加盐解密
     *
     * @param password 初始密码
     * @param dbPassword 数据库中的密码
     * @return 密码是否正确
     */
    public static boolean decrypt(String password, String dbPassword) {
        // 判断长度是否为65是因为：随机盐值是32位，通过md5生成的密码也是32位，$长度1位
        if(StringUtils.hasLength(password) && StringUtils.hasLength(dbPassword)
                && dbPassword.length() == 65 && dbPassword.contains("$")) {
            String[] arr = dbPassword.split("\\$");
            String salt = arr[0];
            String finalPassword = encrypt(password, salt);
            if(finalPassword.equals(dbPassword)) {
                return true;
            }
        }
        return false;
    }

}
