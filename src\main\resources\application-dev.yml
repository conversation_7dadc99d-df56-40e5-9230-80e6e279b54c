# 本地开发环境配置
# 当Nacos配置不可用时，此文件将覆盖Nacos配置

# 服务器配置
server:
  port: 8099
  error:
    include-message: always
    include-binding-errors: always
    include-stacktrace: always

coze:
  streamUrl: http://localhost:8080/api/stream
  url: http://localhost:8080/api
  token: your-coze-token-here
  assistantWorkflowId: your-assistant-workflow-id
  markPaperWorkflowId: your-mark-paper-workflow-id
  brainWorkflowId: your-brain-workflow-id
  makePracticeWorkflowId: your-make-practice-workflow-id

# MinIO配置
minio:
  endpoint: http://localhost:9000
  accessKey: minioadmin
  secretKey: minioadmin
  bucket: net-edu

# KKFileView配置（文件预览服务）
kkfileview:
  url: http://localhost:8012

# 数据库配置
spring:
  datasource:
    url: *********************************************************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  # 允许Bean定义覆盖（解决CORS配置冲突）
  main:
    allow-bean-definition-overriding: true
  # 错误处理配置
  mvc:
    throw-exception-if-no-handler-found: true
  web:
    resources:
      add-mappings: false

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    cache-enabled: false
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
      id-type: auto
  mapper-locations: classpath*:mapper/*.xml

# 日志配置
logging:
  level:
    org.zc.net: debug
    com.baomidou.mybatisplus: debug
    org.springframework.web: debug
    org.springframework.security: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/net.log
    max-size: 10MB
    max-history: 30

# CORS配置
cors:
  allowed-origins: "*"
  allowed-methods: "*"
  allowed-headers: "*"
  allow-credentials: true
  max-age: 3600 