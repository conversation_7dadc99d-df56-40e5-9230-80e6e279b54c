server:
  port: 8099
spring:
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 100MB
  application:
    name: net
  profiles:
    active: dev
  cloud:
    nacos:
      discovery:
        server-addr: ************:8848
        namespace: bab4e9dc-31a9-40c8-82a7-c38c52f79fce
      config:
        server-addr: ************:8848
        namespace: bab4e9dc-31a9-40c8-82a7-c38c52f79fce
        file-extension: yml
        refresh-enabled: true
  config:
    import: nacos:${spring.application.name}-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}