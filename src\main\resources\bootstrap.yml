server:
  port: 8099
spring:
  application:
    name: net
  profiles:
    active: dev
  cloud:
    nacos:
      discovery:
        server-addr: 8.155.45.160:8848
        namespace: bab4e9dc-31a9-40c8-82a7-c38c52f79fce
      config:
        server-addr: 8.155.45.160:8848
        namespace: bab4e9dc-31a9-40c8-82a7-c38c52f79fce
        file-extension: yml
        refresh-enabled: true
        # Comment out the following line to disable Nacos config for local development
        # enabled: false
  # config:
  #   import: nacos:${spring.application.name}-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}