<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTO Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.zc.net.mapper.CourseStudentMapper">
    <select id="selectStudentPage" resultType="org.zc.net.entity.Student">
        select s.id, no, name
        from course_student sc
        left join student s on sc.student_id=s.id
        <where>
            course_id = #{id}
            <if test="no != null and no != ''">
                and no like '%${no}%'
            </if>
            <if test="name != null and name != ''">
                and name like '%${name}%'
            </if>
        </where>
        limit #{size}
        offset #{offset}
    </select>

    <select id="selectStudentCount" resultType="java.lang.Long">
        select count(s.id)
        from course_student sc
        left join student s on sc.student_id=s.id
        <where>
            course_id = #{id}
            <if test="no != null and no != ''">
                and no like '%${no}%'
            </if>
            <if test="name != null and name != ''">
                and name like '%${name}%'
            </if>
        </where>
    </select>
</mapper>