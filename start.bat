@echo off
echo ========================================
echo Net教育项目启动脚本
echo ========================================
echo.

echo 1. 检查Java环境...
java -version
if %errorlevel% neq 0 (
    echo [错误] 未找到Java环境，请确保已安装JDK 21
    echo 请访问: https://adoptium.net/ 下载JDK 21
    pause
    exit /b 1
)
echo [成功] Java环境检查通过
echo.

echo 2. 检查Maven环境...
mvn -version
if %errorlevel% neq 0 (
    echo [错误] 未找到Maven环境，请确保已安装Maven
    echo 请访问: https://maven.apache.org/download.cgi 下载Maven
    pause
    exit /b 1
)
echo [成功] Maven环境检查通过
echo.

echo 3. 检查MySQL服务...
net start | findstr "MySQL" >nul
if %errorlevel% neq 0 (
    echo [警告] MySQL服务可能未启动，请确保MySQL服务正在运行
    echo 可以通过以下方式启动MySQL:
    echo - 服务管理器: services.msc
    echo - 命令行: net start MySQL80
) else (
    echo [成功] MySQL服务检查通过
)
echo.

echo 4. 清理并编译项目...
mvn clean compile
if %errorlevel% neq 0 (
    echo [错误] 项目编译失败，请检查代码或依赖
    pause
    exit /b 1
)
echo [成功] 项目编译完成
echo.

echo 5. 启动Spring Boot应用...
echo [信息] 应用将在 http://localhost:8099 启动
echo [信息] Swagger文档: http://localhost:8099/swagger-ui.html
echo [信息] CORS测试: http://localhost:8099/test/cors
echo.
echo 按 Ctrl+C 停止应用
echo ========================================
mvn spring-boot:run

pause 