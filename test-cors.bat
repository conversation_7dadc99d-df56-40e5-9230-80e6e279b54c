@echo off
echo 测试CORS配置...
echo.

echo 1. 测试GET请求...
curl -X GET http://localhost:8099/test/cors -v

echo.
echo 2. 测试POST请求...
curl -X POST http://localhost:8099/test/cors -H "Content-Type: application/json" -d "test data" -v

echo.
echo 3. 测试OPTIONS请求...
curl -X OPTIONS http://localhost:8099/test/cors -v

echo.
echo 4. 测试学生登录接口...
curl -X POST http://localhost:8099/student/login -H "Content-Type: application/json" -d "{\"username\":\"test\",\"password\":\"test\"}" -v

echo.
echo CORS测试完成！
pause 