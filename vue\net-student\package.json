{"name": "net-system", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "2.0.10", "@microsoft/fetch-event-source": "^2.0.1", "@tinymce/tinymce-vue": "5.1", "@vueuse/core": "^12.7.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "1.2.2", "echarts": "5.4.2", "element-plus": "^2.5.0", "katex": "^0.16.21", "marked": "^15.0.7", "pinia": "2.0.27", "pinia-plugin-persist": "1.0", "sass": "1.57.1", "splitpanes": "^3.1.8", "tinymce": "6.6.2", "vue": "^3.2.41", "vue-router": "4.0.13"}, "devDependencies": {"@vitejs/plugin-vue": "^3.2.0", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.1", "vite": "^3.2.11"}}