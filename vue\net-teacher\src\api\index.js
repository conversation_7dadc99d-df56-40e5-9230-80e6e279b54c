import request from '../utils/request'
import config from '../config'

// 用户信息相关

// 登录
export function login(data) {
  return request.post('/teacher/login', data)
}

// 注册
export function register(data) {
  return request.post('/teacher/register', data)
}

// 获取用户信息
export function getAdmin() {
  return request.get('/teacher')
}

// 修改基本资料
export function changeAdminInfo(data) {
  return request.put('/teacher', data)
}

// 修改密码
export function changeAdminPassword(data) {
  return request.put('/teacher/password', data)
}

// 更新图片地址
export function uploadPictureURL() {
  return config.baseURL + '/resource/upload'
}

// 班课相关

// 获取班课列表
export function getCourseList(params) {
  return request.get('/course', { params })
}

// 获取班课信息
export function getCourse(params) {
  return request.get('/course/' + params.id)
}

// 删除班课
export function delCourse(data) {
  return request.delete('/course/' + data.id)
}

// 添加班课
export function addCourse(data) {
  return request.post('/course', data)
}

// 修改班课
export function editCourse(data) {
  return request.put('/course', data)
}

// 班课学生相关

// 获取班课学生列表
export function getCourseStudentList(params) {
  return request.get('/course/' + params.id + '/student', { params })
}

// 添加班课学生
export function addCourseStudent(data) {
  return request.post('/course/' + data.id + '/student', data.studentIds)
}

// 删除班课学生
export function delCourseStudent(data) {
  return request.delete('/course/' + data.id + '/student/' + data.studentId)
}

// 导出班课学生
export function exportCourseStudent(params) {
  return request.get('/course/' + params.id + '/student/download', { responseType: 'blob' })
}

// 班课资源相关

// 获取班课资源列表
export function getCourseResourceList(params) {
  return request.get('/course/' + params.id + '/resource')
}

// 添加班课资源
export function addCategoryResource(data) {
  return request.post('/category/' + data.id + '/resource', data.resourceIds)
}

// 删除班课资源
export function delCategoryResource(data) {
  return request.delete('/category/' + data.id + '/resource/' + data.resourceId)
}

// 班课测验相关

// 获取班课测验列表
export function getCourseExamList(params) {
  return request.get('/course/' + params.id + '/exam', { params })
}

// 获取测验
export function getExam(params) {
  return request.get('/exam/' + params.id)
}

// 添加测验
export function addExam(data) {
  return request.post('/exam', data)
}

// 修改测验
export function editExam(data) {
  return request.put('/exam', data)
}

// 删除测验
export function delExam(data) {
  return request.delete('/exam/' + data.id)
}

// 资源库相关

// 获取资源列表
export function getResourceList(params) {
  return request.get('/resource', { params })
}

// 删除资源
export function delResource(data) {
  return request.delete('/resource/' + data.id)
}

// 下载资源
export function downResource(params) {
  return request.get('/resource/' + params.id + '/download', { responseType: 'blob' })
}

// 预览资源
export function previewResource(params) {
  return request.get('/resource/' + params.id + '/preview')
}

// 添加资源
export function addResource(data) {
  return request.post('/resource', data)
}

// 获取资源信息
export function getResource(params) {
  return request.get('/resource/' + params.id)
}

// 修改资源信息
export function editResource(data) {
  return request.put('/resource', data)
}

// 题库相关

// 获取题目
export function getQuestion(params) {
  return request.get('/question/' + params.id)
}

// 获取题目列表
export function getQuestionList(params) {
  return request.get('/question', { params })
}

// 添加题目
export function addQuestion(data) {
  return request.post('/question', data)
}

// 修改题目
export function editQuestion(data) {
  return request.put('/question', data)
}

// 删除题目
export function delQuestion(data) {
  return request.delete('/question/' + data.ids)
}

// 分组相关

// 获取分组树
export function getCategoryTree(params) {
  return request.get('/category', { params })
}

// 获取分组
export function getCategory(params) {
  return request.get('/category/' + params.id)
}

// 删除分组
export function delCategory(data) {
  return request.delete('/category/' + data.id)
}

// 添加分组
export function addCategory(data) {
  return request.post('/category', data)
}

// 修改分组
export function editCategory(data) {
  return request.put('/category', data)
}

// 试卷相关

// 获取试题
export function getExamQuestionList(params) {
  return request.get('/exam/' + params.id + '/question')
}

// 添加试题
export function addExamQuestion(data) {
  return request.post('/exam/' + data.id + '/question', data.questionIds)
}

// 删除试题
export function delExamQuestion(data) {
  return request.delete('/exam/' + data.id + '/question/' + data.questionId)
}

// 保存试题
export function saveExamQuestion(data) {
  return request.put('/exam/' + data.id + '/question', data.questions)
}

// 导出试题
export function exportExamQuestion(params) {
  return request.get('/exam/' + params.id + '/question/download', { responseType: 'blob' })
}

// 测验库相关

// 获取测验列表
export function getExamList(params) {
  return request.get('/exam', { params })
}

// 测验学生相关

// 获取学生测验排名
export function getExamRanking(params) {
  return request.get('/exam/' + params.id + '/ranking', { params })
}

// 导出学生测验排名
export function exportExamRanking(params) {
  return request.get('/exam/' + params.id + '/ranking/download', { responseType: 'blob' })
}

// 获取学生测验结果列表
export function getStudentResultList(params) {
  return request.get('/exam/' + params.id + '/result', { params })
}

// 获取测验结果
export function getExamResult(params) {
  return request.get('/exam/' + params.examId + '/student/' + params.studentId + '/result')
}

// 修改测验结果
export function editExamResult(data) {
  return request.put('/exam/' + data.examId + '/student/' + data.studentId + '/result', data.paper)
}

// 导出学生答卷
export function exportStudentPaper(params) {
  return request.get('/exam/' + params.examId + '/student/' + params.studentId + '/download', { responseType: 'blob' })
}

// ai批改测验
export function markPaper(data) {
  return request.post('/chat/exam/' + data.examId + '/student/' + data.studentId)
}


// 获取测验统计数据
export function getExamStatistics(params) {
  return request.get('/exam/' + params.id + '/statistics')
}

// 知识点相关
export function getCourseStudentTag(params) {
  return request.get('/course/' + params.id + '/student/' + params.studentId + '/tag')
}

export function getCourseTag(params) {
  return request.get('/course/' + params.id + '/tag') 
}

export function getTag(params) {
  return request.get('/tag', { params })
}

// 获取知识点节点
export function getNode(params) {
  return request.get('/tag/' + params.id )
}

// 添加知识点节点
export function addNode(data) {
  return request.post('/tag', data)
}

// 修改知识点节点
export function editNode(data) {
  return request.put('/tag', data)
}

// 删除知识点节点
export function delNode(params) {
  return request.delete('/tag/' + params.id )
}

// 获取知识点关系
export function getRelationship(params) {
  return request.get('/tag/relationship/' + params.id )
}

// 添加知识点关系
export function addRelationship(data) {
  return request.post('/tag/relationship', data)
}

// 修改知识点关系
export function editRelationship(data) {
  return request.put('/tag/relationship', data)
}

// 删除知识点关系
export function delRelationship(params) {
  return request.delete('/tag/relationship/' + params.id )
}