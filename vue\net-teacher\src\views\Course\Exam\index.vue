<template>
  <div>
    <!-- 查询表单 -->
    <el-form :model="form" label-width="auto" ref="formRef" inline>
      <el-form-item label="测验名" prop="name">
        <el-input v-model="form.name" style="max-width:250px;" placeholder="请输入测验名"/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="loadExamList" :icon="Search">查询</el-button>
        <el-button type="primary" @click="addRow" :icon="Plus" plain>创建测验</el-button>
      </el-form-item>
    </el-form>
    <!-- 测验列表 -->
    <div>
      <el-empty v-if="!examList.length" description="暂无数据" />
      <el-card shadow="hover" class="list-item" v-for="exam in examList" @click="goToExam(exam)">
        <div class="item-container">
          <div class="icon">
            <el-image src="/images/icon-exam.png" fit="cover" />
          </div>
          <div>
            <div class="name">{{ exam.name }}</div>
            <div class="desc">{{ exam.openTime }} - {{ exam.closeTime }}</div>
          </div>
          <div class="tag">
            <el-tag size="large" type="warning" v-if="new Date() < new Date(exam.openTime)">未开放</el-tag>
            <el-tag size="large" type="primary" v-else-if="new Date() < new Date(exam.closeTime)">已开放</el-tag>
            <el-tag size="large" type="info" v-else>已关闭</el-tag>
          </div>
        </div>
      </el-card>
    </div>
    <el-pagination
      v-model:current-page="pageNum"
      background layout="total, prev, pager, next, jumper"
      :total="total"
      :page-size="pageSize"
      @current-change="handleCurrentChange"
    />
    <!-- 创建测验的弹出框 -->
    <el-dialog v-model="dialogVisible" title="创建测验" :before-close="handleBeforeClose" style="min-width: 400px;">
      <addExam ref="examForm" :courseId="$route.params.id" @success="editSuccess" />
    </el-dialog>
  </div>
</template>

<script setup>
import addExam from './addExam.vue'
import { getCourseExamList } from '@/api'
import { Plus, Search } from '@element-plus/icons-vue'
import { ElMessageBox } from 'element-plus'
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'

// 路由相关
const route = useRoute()
const router = useRouter()
// 当前页测验列表
const examList = ref([])
// 分页相关
const pageNum = ref(1)
const pageSize = ref(10)
const total = ref(0)
// 查询表单
const form = ref({
  name: ''
})
const formRef = ref()

// 弹出框显示
const dialogVisible = ref(false)
// 弹出框中的表单
const examForm = ref()

onMounted(() => {
  loadExamList()
})

// 获取测验列表
const loadExamList = async () => {
  const params = {
    id: route.params.id,
    pageNum: pageNum.value,
    pageSize: pageSize.value,
    name: form.value.name
  }
  const data = await getCourseExamList(params)
  examList.value = data.list
  total.value = data.total
}

// 换页
const handleCurrentChange = value => {
  pageNum.value = value
  loadExamList()
}

// 添加测验
const addRow = () => {
  if (examForm.value) {
    examForm.value.resetForm()
  }
  dialogVisible.value = true
}

// 编辑完成
const editSuccess = () => {
  loadExamList()
  dialogVisible.value = false
}

// 关闭弹出框前
const handleBeforeClose  = () => {
  ElMessageBox.confirm('确定关闭对话框吗？', {
    showClose: false,
    closeOnClickModal: false,
    confirmButtonText: '确定',
    cancelButtonText: '取消',
  }).then(() => {
    dialogVisible.value = false
    setTimeout(() => {
      examForm.value.resetForm()
    }, 500)
  }).catch(() => {})
}

// 进入测验详情界面
const goToExam = row => {
  router.push({ name: 'exam_preview', params: { id: row.id } })
}
</script>

<style lang="scss" scoped>
.list-item:hover {
  background-color: var(--el-menu-hover-bg-color);
}
.list-item {
  cursor: pointer;
  margin-bottom: 20px;
  .item-container {
    display: flex;
    flex-direction: row;
    .icon {
      width: 70px;
      .el-image {
        width: 60px;
        height: 60px;
      }
    }
    .name {
      margin-bottom: 10px;
    }
    .desc {
      color: #737373;
      font-size: 13px;
    }
    .tag {
      width: 60px;
      line-height: 64px;
      margin-left: auto;
    }
  }
}
</style>