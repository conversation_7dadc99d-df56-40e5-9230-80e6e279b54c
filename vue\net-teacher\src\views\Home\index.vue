<template>
  <div style="padding: 20px;">
    <el-row :gutter="20" style="margin-bottom: 20px;">
      <el-col :span="8">
        <router-link :to="{ name: 'courselib' }">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>班课</span>
                <el-icon><Management /></el-icon>
              </div>
            </template>
            <div class="num">{{ courseTotal }}</div>
          </el-card>
        </router-link>
      </el-col>
      <el-col :span="8">
        <router-link :to="{ name: 'resourcelib' }">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>资源</span>
                <el-icon><Box /></el-icon>
              </div>
            </template>
            <div class="num">{{ resourceTotal }}</div>
          </el-card>
        </router-link>
      </el-col>
      <el-col :span="8">
        <router-link :to="{ name: 'examlib' }">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>测验/作业</span>
                <el-icon><Platform /></el-icon>
              </div>
            </template>
            <div class="num">{{ examTotal }}</div>
          </el-card>
        </router-link>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card style="min-height: 480px;">
          <template #header>
            <div class="card-header">
              <span>最近的班课</span>
              <router-link :to="{ name: 'courselib' }">
                <el-link>查看更多</el-link>
              </router-link>
            </div>
          </template>
          <el-empty v-if="!courseList.length" description="暂无数据" />
          <el-card shadow="hover" class="list-item" v-for="course in courseList" @click="goToCourse(course)">
            <div class="item-container">
              <div class="icon">
                <el-image :src="config.resourceURL + course.cover" fit="cover" />
              </div>
              <div>
                <div class="name">{{ course.name }}</div>
                <div class="desc">{{ course.clazz }}</div>
              </div>
            </div>
          </el-card>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card style="min-height: 480px;">
          <template #header>
            <div class="card-header">
              <span>最近的测验/作业</span>
              <router-link :to="{ name: 'examlib' }">
                <el-link>查看更多</el-link>
              </router-link>
            </div>
          </template>
          <el-empty v-if="!examList.length" description="暂无数据" />
          <el-card shadow="hover" class="list-item" v-for="exam in examList" @click="goToExam(exam)">
            <div class="item-container">
              <div class="icon">
                <el-image src="/images/icon-exam.png" fit="cover" />
              </div>
              <div>
                <div class="name">{{ exam.name }}</div>
                <div class="desc">{{ exam.openTime }} - {{ exam.closeTime }}</div>
              </div>
            </div>
          </el-card>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { getCourseList, getExamList, getResourceList } from '@/api'
import config from '@/config'
import { Management, Box, Platform } from '@element-plus/icons-vue'
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'

// 路由相关
const router = useRouter()
// 分页相关
const pageNum = ref(1)
const pageSize = ref(3)
const courseTotal = ref(0)
const resourceTotal = ref(0)
const examTotal = ref(0)

const courseList = ref([])
const examList = ref([])

onMounted(() => {
  loadCourseList()
  loadResourceList()
  loadExamList()
})

// 获取班课列表
const loadCourseList = async () => {
  const params = {
    pageNum: pageNum.value,
    pageSize: pageSize.value
  }
  const data = await getCourseList(params)
  courseList.value = data.list
  courseTotal.value = data.total
}

// 获取资源列表
const loadResourceList = async () => {
  const params = {
    pageNum: pageNum.value,
    pageSize: pageSize.value
  }
  const data = await getResourceList(params)
  resourceTotal.value = data.total
}

// 获取测验列表
const loadExamList = async () => {
  const params = {
    pageNum: pageNum.value,
    pageSize: pageSize.value
  }
  const data = await getExamList(params)
  examList.value = data.list
  examTotal.value = data.total
}

// 进入班课详情界面
const goToCourse = row => {
  router.push({ name: 'course_exam', params: { id: row.id } })
}

// 进入测验详情界面
const goToExam = row => {
  router.push({ name: 'exam', params: { id: row.id } })
}
</script>

<style lang="scss" scoped>
.card-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}
.num {
  font-weight: 700;
  font-size: 1.5rem;
  line-height: 2rem;
}
.list-item:hover {
  background-color: var(--el-menu-hover-bg-color);
}
.list-item {
  cursor: pointer;
  margin-bottom: 20px;
  .item-container {
    display: flex;
    flex-direction: row;
    .icon {
      width: 70px;
      .el-image {
        width: 60px;
        height: 60px;
      }
    }
    .name {
      margin-bottom: 10px;
    }
    .desc {
      color: #737373;
      font-size: 13px;
    }
  }
}
</style>